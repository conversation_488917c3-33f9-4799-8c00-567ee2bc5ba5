#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI智能QQ机器人 + 抽奖系统
集成OpenAI大模型和抽奖功能
"""

import asyncio
import botpy
from botpy import logging
import config
from ai_assistant import ai_assistant
from lottery_system import lottery_system
import datetime
import time
import re

# 设置日志级别
_log = logging.get_logger()


class AILotteryBotClient(botpy.Client):
    """AI智能机器人 + 抽奖系统客户端类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ai_assistant = ai_assistant
        self.lottery_system = lottery_system
        self._msg_seq_counter = 0
        
        # 初始化抽奖管理员
        for admin_id in config.LOTTERY_ADMINS:
            self.lottery_system.add_admin(admin_id)
        
        # 预定义指令列表（这些指令不会发送给AI）
        self.predefined_commands = [
            "帮助", "help", "时间", "time", "ping", "状态", "status",
            "清除历史", "clear", "ai状态", "ai_status",
            # 抽奖相关指令
            "抽奖帮助", "lottery_help", "创建抽奖", "开始抽奖", "取消抽奖",
            "抽奖状态", "手动开奖", "抽奖历史", "添加管理员", "管理员列表",
            # 用户ID查询指令
            "我的id", "查询id", "用户id", "my_id"
        ]
    
    def _get_unique_msg_seq(self):
        """生成唯一的消息序号"""
        self._msg_seq_counter += 1
        return self._msg_seq_counter
    
    def _get_user_id(self, message):
        """获取用户ID，兼容不同的消息类型"""
        author = message.author
        user_id = (
            getattr(author, 'id', None) or
            getattr(author, 'openid', None) or
            getattr(author, 'user_openid', None) or
            getattr(author, 'member_openid', None) or
            str(hash(str(author)))
        )
        return str(user_id)

    def _is_group_owner(self, message):
        """检查用户是否为群主"""
        # 尝试获取用户角色信息
        author = message.author

        # 检查可能的群主标识属性
        role = getattr(author, 'role', None)
        member_role = getattr(author, 'member_role', None)
        roles = getattr(author, 'roles', [])

        # 检查是否为群主
        if role == 'owner' or role == 'admin':
            return True
        if member_role == 'owner' or member_role == 'admin':
            return True
        if isinstance(roles, list) and ('owner' in roles or 'admin' in roles):
            return True

        # 如果无法确定，返回False（保守处理）
        return False
    
    async def on_ready(self):
        """当机器人启动完成时触发"""
        print(f"✅ AI智能机器人+抽奖系统 {self.robot.name} 已启动!")
        print(f"📱 机器人ID: {self.robot.id}")
        
        # 检查AI功能状态
        if self.ai_assistant.is_enabled():
            print("🤖 AI功能已启用")
            status = self.ai_assistant.get_status()
            print(f"   模型: {status['model']}")
        else:
            print("⚠️ AI功能未启用")
        
        # 检查抽奖功能状态
        if config.LOTTERY_ENABLED:
            print("🎲 抽奖功能已启用")
            admin_count = len(self.lottery_system.get_admin_list())
            print(f"   管理员数量: {admin_count}")
        else:
            print("⚠️ 抽奖功能未启用")
        
        print("🎯 机器人正在等待消息...")
        print("\n📋 功能说明:")
        print("• @机器人 + 任意消息 - AI智能回复")
        print("• @机器人 + 抽奖指令 - 抽奖功能")
        print("• 自动记录群聊消息用于抽奖")
        print("-" * 50)
    
    def _is_predefined_command(self, content: str) -> bool:
        """检查是否为预定义指令"""
        content_lower = content.lower().strip()
        return any(cmd in content_lower for cmd in self.predefined_commands)
    
    def _is_lottery_command(self, content: str) -> bool:
        """检查是否为抽奖相关指令"""
        lottery_keywords = [
            "抽奖", "lottery", "创建抽奖", "开始抽奖", "取消抽奖", 
            "抽奖状态", "手动开奖", "抽奖历史", "添加管理员", "管理员列表"
        ]
        content_lower = content.lower().strip()
        return any(keyword in content_lower for keyword in lottery_keywords)
    
    async def _handle_lottery_command(self, message, content: str):
        """处理抽奖相关指令"""
        content_lower = content.lower().strip()
        user_id = self._get_user_id(message)
        group_id = getattr(message, 'group_openid', 'unknown_group')
        is_group_owner = self._is_group_owner(message)
        
        try:
            if "抽奖帮助" in content_lower or "lottery_help" in content_lower:
                help_text = """
🎲 抽奖系统帮助：

👑 管理员指令（群主和配置的管理员）：
• 创建抽奖 [天数] [开奖时间] - 创建抽奖活动
  例：创建抽奖 7 2小时后
  例：创建抽奖 3 2024-01-01 20:00
• 取消抽奖 - 取消当前抽奖活动
• 手动开奖 - 立即开奖
• 添加管理员 - 查看添加管理员指导

👥 用户指令：
• 抽奖状态 - 查看当前抽奖状态
• 抽奖历史 - 查看历史抽奖记录
• 管理员列表 - 查看抽奖管理员
• 我的id - 查询自己的用户ID

📝 说明：
• 群主自动拥有管理员权限
• 系统自动记录群聊消息
• 重复消息不参与抽奖
• 只有管理员或群主可以创建和管理抽奖
                """
                await message.reply(content=help_text, msg_seq=self._get_unique_msg_seq())
                
            elif "创建抽奖" in content or "开始抽奖" in content:
                # 解析创建抽奖指令
                # 格式：创建抽奖 [天数] [开奖时间]
                parts = content.split()
                if len(parts) < 3:
                    await message.reply(
                        content="❌ 指令格式错误\n正确格式：创建抽奖 [天数] [开奖时间]\n例：创建抽奖 7 2小时后",
                        msg_seq=self._get_unique_msg_seq()
                    )
                    return
                
                try:
                    days = int(parts[1])
                    draw_time = " ".join(parts[2:])
                except ValueError:
                    await message.reply(
                        content="❌ 天数必须是数字",
                        msg_seq=self._get_unique_msg_seq()
                    )
                    return
                
                success, result_msg = self.lottery_system.create_lottery(group_id, user_id, days, draw_time, is_group_owner)
                await message.reply(content=result_msg, msg_seq=self._get_unique_msg_seq())

            elif "取消抽奖" in content_lower:
                success, result_msg = self.lottery_system.cancel_lottery(group_id, user_id, is_group_owner)
                await message.reply(content=result_msg, msg_seq=self._get_unique_msg_seq())

            elif "手动开奖" in content_lower:
                success, result_msg = self.lottery_system.draw_lottery(group_id, user_id, is_group_owner)
                await message.reply(content=result_msg, msg_seq=self._get_unique_msg_seq())
                
            elif "抽奖状态" in content_lower:
                status_msg = self.lottery_system.get_lottery_status(group_id)
                await message.reply(content=status_msg, msg_seq=self._get_unique_msg_seq())
                
            elif "抽奖历史" in content_lower:
                history_msg = self.lottery_system.get_lottery_history(group_id)
                await message.reply(content=history_msg, msg_seq=self._get_unique_msg_seq())
                
            elif "添加管理员" in content_lower:
                if not self.lottery_system.is_admin(user_id, is_group_owner):
                    await message.reply(
                        content="❌ 只有现有管理员或群主才能添加新管理员",
                        msg_seq=self._get_unique_msg_seq()
                    )
                    return

                # 提供添加管理员的指导
                admin_guide = """
💡 添加管理员方法：

1️⃣ 让新管理员发送：@机器人 我的id
2️⃣ 复制返回的用户ID
3️⃣ 联系机器人维护者将ID添加到config.py的LOTTERY_ADMINS列表中
4️⃣ 重启机器人生效

📝 注意：群主始终拥有管理员权限，无需额外配置
                """
                await message.reply(
                    content=admin_guide,
                    msg_seq=self._get_unique_msg_seq()
                )
                
            elif "管理员列表" in content_lower:
                admins = self.lottery_system.get_admin_list()
                if admins:
                    admin_text = "👑 抽奖管理员列表：\n" + "\n".join(f"• {admin}" for admin in admins)
                else:
                    admin_text = "📝 暂无抽奖管理员"
                await message.reply(content=admin_text, msg_seq=self._get_unique_msg_seq())
                
            else:
                await message.reply(
                    content="❓ 未知的抽奖指令，发送'抽奖帮助'查看可用指令",
                    msg_seq=self._get_unique_msg_seq()
                )
                
        except Exception as e:
            print(f"❌ 处理抽奖指令时出错: {e}")
            await message.reply(
                content="❌ 处理抽奖指令时出现错误",
                msg_seq=self._get_unique_msg_seq()
            )
    
    async def _handle_predefined_command(self, message, content: str):
        """处理预定义指令"""
        content_lower = content.lower().strip()
        
        # 如果是抽奖指令，转发给抽奖处理器
        if self._is_lottery_command(content):
            await self._handle_lottery_command(message, content)
            return
        
        try:
            if "帮助" in content_lower or "help" in content_lower:
                help_text = """
🤖 AI智能机器人+抽奖系统帮助：

📝 基础指令：
• 帮助/help - 显示此帮助信息
• 时间/time - 显示当前时间
• ping - 测试机器人响应
• 状态/status - 显示机器人状态
• ai状态 - 显示AI功能状态
• 清除历史/clear - 清除对话历史

🧠 AI功能：
• 直接@我说话 - AI智能回复
• 支持上下文对话记忆

🎲 抽奖功能：
• 抽奖帮助 - 查看抽奖系统帮助
• 抽奖状态 - 查看当前抽奖状态
• 抽奖历史 - 查看历史记录

💡 提示：除了上述指令外，任何消息都会发送给AI进行智能回复
                """
                await message.reply(content=help_text, msg_seq=self._get_unique_msg_seq())
                
            elif "时间" in content_lower or "time" in content_lower:
                now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                await message.reply(content=f"⏰ 当前时间: {now}", msg_seq=self._get_unique_msg_seq())
                
            elif "ping" in content_lower:
                await message.reply(content="pong! 🏓 机器人运行正常", msg_seq=self._get_unique_msg_seq())

            elif "我的id" in content_lower or "查询id" in content_lower or "用户id" in content_lower or "my_id" in content_lower:
                user_id = self._get_user_id(message)
                id_info = f"""
🆔 用户ID信息：
• 你的用户ID: {user_id}
• 用途: 用于配置抽奖管理员
• 配置方法: 将此ID添加到config.py的LOTTERY_ADMINS列表中

💡 提示: 请将此ID发送给机器人维护者以获得管理员权限
                """
                await message.reply(content=id_info, msg_seq=self._get_unique_msg_seq())
                
            elif "状态" in content_lower or "status" in content_lower:
                ai_status = "✅ 已启用" if self.ai_assistant.is_enabled() else "❌ 未启用"
                lottery_status = "✅ 已启用" if config.LOTTERY_ENABLED else "❌ 未启用"
                status_text = f"""
📊 机器人状态：
• 机器人名称: {self.robot.name}
• 机器人ID: {self.robot.id}
• AI功能: {ai_status}
• 抽奖功能: {lottery_status}
• 运行状态: ✅ 正常
                """
                await message.reply(content=status_text, msg_seq=self._get_unique_msg_seq())
                
            elif "ai状态" in content_lower:
                if self.ai_assistant.is_enabled():
                    status = self.ai_assistant.get_status()
                    ai_status_text = f"""
🤖 AI功能状态：
• 状态: ✅ 已启用
• 模型: {status['model']}
• 最大tokens: {status['max_tokens']}
• 创造性: {status['temperature']}
• 缓存对话数: {status['cached_conversations']}
                    """
                else:
                    ai_status_text = "❌ AI功能未启用，请检查配置文件"
                await message.reply(content=ai_status_text, msg_seq=self._get_unique_msg_seq())
                
            elif "清除历史" in content_lower or "clear" in content_lower:
                user_id = self._get_user_id(message)
                self.ai_assistant.clear_conversation_history(user_id)
                await message.reply(content="✅ 已清除你的对话历史", msg_seq=self._get_unique_msg_seq())
                
            else:
                await message.reply(content="未知指令，发送'帮助'查看可用指令", msg_seq=self._get_unique_msg_seq())
                
        except Exception as e:
            print(f"❌ 处理预定义指令时出错: {e}")
            try:
                await message.reply(content="❌ 处理指令时出现错误", msg_seq=self._get_unique_msg_seq())
            except:
                pass
    
    async def _handle_ai_message(self, message, content: str):
        """处理AI消息"""
        if not self.ai_assistant.is_enabled():
            try:
                await message.reply(content="❌ AI功能未启用，请联系管理员配置OpenAI设置", msg_seq=self._get_unique_msg_seq())
            except Exception as e:
                print(f"❌ 回复AI未启用消息失败: {e}")
            return
        
        try:
            # 显示正在思考的提示
            await message.reply(content="🤔 正在思考中...", msg_seq=self._get_unique_msg_seq())
            
            # 获取用户ID作为对话标识
            user_id = self._get_user_id(message)
            
            # 调用AI获取回复
            ai_response = await self.ai_assistant.get_ai_response(content, user_id)
            
            if ai_response:
                # 发送AI回复
                await message.reply(content=f"🤖 {ai_response}", msg_seq=self._get_unique_msg_seq())
                print(f"✅ AI回复成功: {content[:30]}... -> {ai_response[:30]}...")
                _log.info(f"AI回复成功: 用户{user_id}")
            else:
                await message.reply(content="❌ AI回复生成失败，请稍后重试", msg_seq=self._get_unique_msg_seq())
                print("❌ AI回复生成失败")
                _log.error("AI回复生成失败")
                
        except Exception as e:
            try:
                await message.reply(content="❌ 处理消息时出现错误，请稍后重试", msg_seq=self._get_unique_msg_seq())
            except:
                pass
            print(f"❌ 处理AI消息时出错: {e}")
            _log.error(f"处理AI消息时出错: {e}")
    
    async def on_group_at_message_create(self, message):
        """当机器人在群聊中被@时触发"""
        content = message.content.strip()
        print(f"📨 收到群聊@消息: {content}")
        _log.info(f"收到群聊@消息: {content}")
        
        # 检查是否为预定义指令
        if self._is_predefined_command(content):
            await self._handle_predefined_command(message, content)
        else:
            # 发送给AI处理
            await self._handle_ai_message(message, content)
    
    async def on_group_message_create(self, message):
        """当群聊中有新消息时触发（用于记录抽奖消息）"""
        if not config.LOTTERY_ENABLED:
            return
        
        try:
            # 记录消息用于抽奖（排除机器人自己的消息）
            if hasattr(message, 'author') and message.author:
                user_id = self._get_user_id(message)
                group_id = getattr(message, 'group_openid', 'unknown_group')
                content = getattr(message, 'content', '')
                
                # 排除@机器人的消息和空消息
                if content and not content.startswith('@') and len(content.strip()) > 0:
                    self.lottery_system.record_message(group_id, user_id, content)
                    
        except Exception as e:
            print(f"❌ 记录抽奖消息时出错: {e}")
    
    async def on_c2c_message_create(self, message):
        """当收到私聊消息时触发"""
        content = message.content.strip()
        print(f"📩 收到私聊消息: {content}")
        _log.info(f"收到私聊消息: {content}")
        
        # 私聊消息也支持AI回复和指令
        if self._is_predefined_command(content):
            await self._handle_predefined_command(message, content)
        else:
            await self._handle_ai_message(message, content)


async def main():
    """主函数"""
    print("🚀 AI智能机器人+抽奖系统启动中...")
    print("=" * 50)
    
    # 使用默认的 intents
    intents = botpy.Intents.default()
    
    client = AILotteryBotClient(intents=intents, is_sandbox=config.IS_SANDBOX)
    
    try:
        # 启动机器人
        await client.start(appid=config.BOT_ID, secret=config.BOT_SECRET)
    except Exception as e:
        print(f"❌ AI智能机器人+抽奖系统启动失败: {e}")
        _log.error(f"AI智能机器人+抽奖系统启动失败: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 AI智能机器人+抽奖系统已停止")
    except Exception as e:
        print(f"❌ AI智能机器人+抽奖系统运行失败: {e}")
