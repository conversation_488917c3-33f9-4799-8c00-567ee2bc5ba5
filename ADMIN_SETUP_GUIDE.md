# 👑 QQ机器人管理员配置指南

## 📋 概述

要使用抽奖功能，需要先配置抽奖管理员。只有管理员才能创建、取消和管理抽奖活动。

## 🔍 获取用户ID

### 方法1：使用机器人查询功能（推荐）

1. **启动机器人**：
   ```bash
   C:/Python311/python.exe ai_lottery_bot.py
   ```

2. **在群聊中查询用户ID**：
   让想要成为管理员的用户在群聊中@机器人发送：
   ```
   @机器人 我的id
   ```
   或
   ```
   @机器人 查询id
   ```

3. **机器人会回复用户ID**：
   ```
   🆔 用户ID信息：
   • 你的用户ID: 8CC91A5198960CE8A98498065FA79F87
   • 用途: 用于配置抽奖管理员
   • 配置方法: 将此ID添加到config.py的LOTTERY_ADMINS列表中
   
   💡 提示: 请将此ID发送给机器人维护者以获得管理员权限
   ```

4. **复制用户ID**：
   复制回复中的用户ID（如：`8CC91A5198960CE8A98498065FA79F87`）

### 方法2：通过机器人日志获取

1. **启动机器人并观察日志**
2. **让用户@机器人发送任意消息**
3. **在机器人输出中查找用户ID**：
   ```
   📨 收到群聊@消息: 你好
   🔍 用户ID: 8CC91A5198960CE8A98498065FA79F87
   ```

## ⚙️ 配置管理员

### 1. 编辑配置文件

打开 `config.py` 文件，找到抽奖配置部分：

```python
# 抽奖系统配置
LOTTERY_ENABLED = True  # 是否启用抽奖功能
LOTTERY_ADMINS = [  # 抽奖管理员用户ID列表
    # "admin_user_id_1",
    # "admin_user_id_2",
    # 请添加管理员的用户ID
]
```

### 2. 添加管理员ID

将获取到的用户ID添加到 `LOTTERY_ADMINS` 列表中：

```python
# 抽奖系统配置
LOTTERY_ENABLED = True  # 是否启用抽奖功能
LOTTERY_ADMINS = [  # 抽奖管理员用户ID列表
    "8CC91A5198960CE8A98498065FA79F87",  # 第一个管理员
    "另一个用户ID",                      # 第二个管理员（如果有）
    # 可以添加更多管理员
]
```

### 3. 保存并重启机器人

1. **保存配置文件**
2. **重启机器人**：
   ```bash
   # 停止当前运行的机器人（Ctrl+C）
   # 然后重新启动
   C:/Python311/python.exe ai_lottery_bot.py
   ```

## ✅ 验证配置

### 1. 检查启动日志

机器人启动时会显示管理员数量：
```
✅ AI智能机器人+抽奖系统 早鸟-测试中 已启动!
🎲 抽奖功能已启用
   管理员数量: 1
```

### 2. 测试管理员权限

让配置的管理员在群聊中测试：

```
@机器人 管理员列表
```

应该看到类似回复：
```
👑 抽奖管理员列表：
• 8CC91A5198960CE8A98498065FA79F87
```

### 3. 测试创建抽奖

管理员可以尝试创建抽奖：
```
@机器人 创建抽奖 7 1小时后
```

如果配置正确，会看到成功消息：
```
✅ 抽奖活动创建成功！
📅 选取范围: 最近7天的消息
⏰ 开奖时间: 2024-01-01 21:00:00
🎫 抽奖ID: group123_1704117600
```

## 🔧 配置示例

### 单个管理员配置
```python
LOTTERY_ENABLED = True
LOTTERY_ADMINS = [
    "8CC91A5198960CE8A98498065FA79F87"
]
```

### 多个管理员配置
```python
LOTTERY_ENABLED = True
LOTTERY_ADMINS = [
    "8CC91A5198960CE8A98498065FA79F87",  # 主管理员
    "另一个用户ID123456789",            # 副管理员
    "第三个用户ID987654321"             # 其他管理员
]
```

### 禁用抽奖功能
```python
LOTTERY_ENABLED = False  # 禁用抽奖功能
LOTTERY_ADMINS = []      # 清空管理员列表
```

## ⚠️ 注意事项

### 安全性
1. **妥善保管管理员权限** - 只给信任的用户管理员权限
2. **定期检查管理员列表** - 移除不再需要的管理员
3. **备份配置文件** - 定期备份 `config.py` 文件

### 用户ID格式
1. **用户ID是字符串** - 必须用引号包围
2. **区分大小写** - 用户ID是区分大小写的
3. **完整复制** - 确保完整复制用户ID，不要遗漏字符

### 常见错误
1. **忘记引号** - 用户ID必须用引号包围
2. **多余的逗号** - 最后一个管理员ID后不要加逗号
3. **格式错误** - 确保Python语法正确

## 🎯 快速配置流程

1. **启动机器人** → `python ai_lottery_bot.py`
2. **获取用户ID** → `@机器人 我的id`
3. **编辑配置** → 在 `config.py` 中添加用户ID
4. **重启机器人** → 停止并重新启动
5. **验证权限** → `@机器人 管理员列表`
6. **测试功能** → `@机器人 创建抽奖 7 1小时后`

## 📞 故障排除

### 如果管理员权限不生效
1. 检查用户ID是否正确复制
2. 确认配置文件语法正确
3. 确认已重启机器人
4. 检查 `LOTTERY_ENABLED = True`

### 如果无法获取用户ID
1. 确认机器人正常运行
2. 确认在群聊中@机器人
3. 检查机器人日志输出
4. 尝试发送其他消息测试机器人响应

现在你可以按照这个指南配置抽奖管理员了！👑✨
