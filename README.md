# QQ机器人项目

这是一个基于Python的QQ机器人项目，使用官方的qq-botpy SDK开发。

## 功能特性

- ✅ 群聊@消息回复
- ✅ 私聊消息处理
- ✅ 基本命令响应（你好、时间、帮助、ping）
- ✅ Hello消息发送功能
- 🤖 **AI智能对话** - 集成OpenAI大模型
- 💬 **上下文记忆** - 支持多轮对话
- 🎯 **智能指令识别** - 区分预定义指令和AI对话
- 🎲 **抽奖系统** - 完整的群聊抽奖功能
- 👑 **权限管理** - 管理员权限控制
- 📊 **数据统计** - 消息记录和抽奖历史
- ✅ 日志记录
- ✅ 沙箱环境支持

## 安装依赖

```bash
pip install -r requirements.txt
```

如果遇到版本问题，也可以直接安装：
```bash
pip install qq-botpy==1.2.1 aiohttp pydantic openai tiktoken
```

## 配置说明

在 `config.py` 文件中配置你的机器人信息：

- `BOT_QQ`: 机器人QQ号
- `BOT_ID`: 机器人ID  
- `BOT_TOKEN`: 机器人令牌
- `BOT_SECRET`: 机器人密钥
- `IS_SANDBOX`: 是否为沙箱环境（开发测试时设为True）

### AI功能配置（可选）

- `OPENAI_API_KEY`: OpenAI API密钥
- `OPENAI_API_BASE`: API地址（默认OpenAI官方）
- `OPENAI_MODEL`: 使用的模型（如gpt-3.5-turbo）
- `AI_ENABLED`: 是否启用AI功能

### 抽奖功能配置（可选）

- `LOTTERY_ENABLED`: 是否启用抽奖功能
- `LOTTERY_ADMINS`: 抽奖管理员用户ID列表

## 运行机器人

### 基础机器人
```bash
python bot.py
```

### Hello机器人（专门用于测试发送hello消息）
```bash
python hello_bot.py
```

### AI智能机器人（推荐）🤖
```bash
python ai_bot.py
```

### AI智能机器人+抽奖系统（最新）🎲
```bash
python ai_lottery_bot.py
```

### 友好启动脚本
```bash
python start.py
```

## 支持的命令

在群聊中@机器人并发送以下内容：

- `你好` - 机器人会回复问候
- `时间` - 显示当前时间
- `帮助` - 显示帮助菜单
- `ping` - 测试机器人响应
- `发送hello` - 在群里发送hello消息

## AI智能对话功能 🤖

### 预定义指令
- `帮助` - 显示AI机器人帮助信息
- `时间` - 显示当前时间
- `状态` - 显示机器人运行状态
- `ai状态` - 显示AI功能状态
- `清除历史` - 清除个人对话历史

### AI对话
- 除了预定义指令外，任何@机器人的消息都会发送给AI进行智能回复
- 支持上下文对话记忆
- 每个用户独立的对话历史

## 抽奖系统功能 🎲

### 管理员指令
- `创建抽奖 [天数] [开奖时间]` - 创建抽奖活动
  - 例：`创建抽奖 7 2小时后`
  - 例：`创建抽奖 3 2024-01-01 20:00`
- `取消抽奖` - 取消当前抽奖活动
- `手动开奖` - 立即开奖
- `管理员列表` - 查看抽奖管理员

### 用户指令
- `抽奖帮助` - 查看抽奖系统帮助
- `抽奖状态` - 查看当前抽奖状态
- `抽奖历史` - 查看历史抽奖记录

### 抽奖规则
- 自动记录群聊消息（排除重复内容）
- 只有管理员可以创建和管理抽奖
- 支持灵活的时间设置（相对时间和绝对时间）
- 随机选择获奖消息，公平公正

## Hello消息功能

机器人现在支持在群聊中发送hello消息：

1. **自动发送**：当机器人被添加到新群聊时，会自动发送hello欢迎消息
2. **手动发送**：在群聊中@机器人并发送 `发送hello` 或 `hello` 命令
3. **专用机器人**：使用 `hello_bot.py` 运行专门的Hello机器人

## 注意事项

1. 请妥善保管机器人的令牌和密钥，不要泄露给他人
2. 开发测试时建议使用沙箱环境
3. 确保机器人已被添加到相应的群聊中
4. 机器人需要相应的权限才能正常工作

## 开发说明

### 核心文件
- `ai_lottery_bot.py` - 🎲 AI智能机器人+抽奖系统（最新推荐）
- `ai_bot.py` - 🤖 AI智能机器人
- `bot.py` - 基础机器人逻辑
- `hello_bot.py` - 专门用于发送Hello消息的机器人
- `simple_hello_bot.py` - 简化版Hello机器人

### AI相关
- `ai_assistant.py` - AI助手核心模块
- `AI_SETUP_GUIDE.md` - AI功能配置指南

### 抽奖系统
- `lottery_system.py` - 抽奖系统核心模块
- `LOTTERY_GUIDE.md` - 抽奖功能使用指南
- `test_lottery_system.py` - 抽奖系统测试脚本

### 工具文件
- `start.py` - 友好的启动脚本
- `test_bot.py` - 测试脚本
- `config.py` - 配置文件
- `requirements.txt` - 依赖包列表

## 🚀 快速开始AI功能

1. **配置OpenAI API Key**：
   ```python
   # 在config.py中设置
   OPENAI_API_KEY = "sk-your-api-key-here"
   AI_ENABLED = True
   ```

2. **启动AI机器人**：
   ```bash
   python ai_bot.py
   ```

3. **测试AI对话**：
   - 在群聊中@机器人说话
   - 机器人会智能回复

详细配置请参考 `AI_SETUP_GUIDE.md`

## 🎲 快速开始抽奖功能

1. **配置抽奖管理员**：
   ```python
   # 在config.py中设置
   LOTTERY_ENABLED = True
   LOTTERY_ADMINS = ["your_user_id_here"]
   ```

2. **启动AI+抽奖机器人**：
   ```bash
   python ai_lottery_bot.py
   ```

3. **测试抽奖功能**：
   - 在群聊中@机器人发送 `抽奖帮助`
   - 管理员可以创建抽奖：`创建抽奖 7 2小时后`

详细使用请参考 `LOTTERY_GUIDE.md`
