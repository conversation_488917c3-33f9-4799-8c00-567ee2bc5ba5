# QQ机器人项目

这是一个基于Python的QQ机器人项目，使用官方的qq-botpy SDK开发。

## 功能特性

- ✅ 群聊@消息回复
- ✅ 私聊消息处理
- ✅ 基本命令响应（你好、时间、帮助、ping）
- ✅ Hello消息发送功能
- ✅ 日志记录
- ✅ 沙箱环境支持

## 安装依赖

```bash
pip install -r requirements.txt
```

如果遇到版本问题，也可以直接安装：
```bash
pip install qq-botpy==1.2.1 aiohttp pydantic
```

## 配置说明

在 `config.py` 文件中配置你的机器人信息：

- `BOT_QQ`: 机器人QQ号
- `BOT_ID`: 机器人ID  
- `BOT_TOKEN`: 机器人令牌
- `BOT_SECRET`: 机器人密钥
- `IS_SANDBOX`: 是否为沙箱环境（开发测试时设为True）

## 运行机器人

### 基础机器人
```bash
python bot.py
```

### Hello机器人（专门用于测试发送hello消息）
```bash
python hello_bot.py
```

### 友好启动脚本
```bash
python start.py
```

## 支持的命令

在群聊中@机器人并发送以下内容：

- `你好` - 机器人会回复问候
- `时间` - 显示当前时间
- `帮助` - 显示帮助菜单
- `ping` - 测试机器人响应
- `发送hello` - 在群里发送hello消息

## Hello消息功能

机器人现在支持在群聊中发送hello消息：

1. **自动发送**：当机器人被添加到新群聊时，会自动发送hello欢迎消息
2. **手动发送**：在群聊中@机器人并发送 `发送hello` 或 `hello` 命令
3. **专用机器人**：使用 `hello_bot.py` 运行专门的Hello机器人

## 注意事项

1. 请妥善保管机器人的令牌和密钥，不要泄露给他人
2. 开发测试时建议使用沙箱环境
3. 确保机器人已被添加到相应的群聊中
4. 机器人需要相应的权限才能正常工作

## 开发说明

- `bot.py` - 主要的机器人逻辑
- `hello_bot.py` - 专门用于发送Hello消息的机器人
- `start.py` - 友好的启动脚本
- `test_bot.py` - 测试脚本
- `config.py` - 配置文件
- `requirements.txt` - 依赖包列表
