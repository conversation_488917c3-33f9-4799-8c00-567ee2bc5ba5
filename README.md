# QQ机器人项目

这是一个基于Python的QQ机器人项目，使用官方的qq-botpy SDK开发。

## 功能特性

- ✅ 群聊@消息回复
- ✅ 私聊消息处理
- ✅ 基本命令响应（你好、时间、帮助、ping）
- ✅ 日志记录
- ✅ 沙箱环境支持

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

在 `config.py` 文件中配置你的机器人信息：

- `BOT_QQ`: 机器人QQ号
- `BOT_ID`: 机器人ID  
- `BOT_TOKEN`: 机器人令牌
- `BOT_SECRET`: 机器人密钥
- `IS_SANDBOX`: 是否为沙箱环境（开发测试时设为True）

## 运行机器人

```bash
python bot.py
```

## 支持的命令

在群聊中@机器人并发送以下内容：

- `你好` - 机器人会回复问候
- `时间` - 显示当前时间
- `帮助` - 显示帮助菜单
- `ping` - 测试机器人响应

## 注意事项

1. 请妥善保管机器人的令牌和密钥，不要泄露给他人
2. 开发测试时建议使用沙箱环境
3. 确保机器人已被添加到相应的群聊中
4. 机器人需要相应的权限才能正常工作

## 开发说明

- `bot.py` - 主要的机器人逻辑
- `config.py` - 配置文件
- `requirements.txt` - 依赖包列表
