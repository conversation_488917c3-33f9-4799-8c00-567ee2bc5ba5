#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QQ机器人测试脚本
用于测试机器人的基本功能
"""

import asyncio
import botpy
from botpy import logging
import config

# 设置日志级别
_log = logging.get_logger()


class TestClient(botpy.Client):
    """测试用的QQ机器人客户端类"""
    
    async def on_ready(self):
        """当机器人启动完成时触发"""
        print(f"✅ 机器人 {self.robot.name} 已启动!")
        print(f"📱 机器人ID: {self.robot.id}")
        print("🎯 机器人正在等待消息...")
        print("\n📋 测试说明:")
        print("1. 将机器人添加到测试群聊")
        print("2. 在群聊中@机器人并发送消息")
        print("3. 观察机器人的回复")
        print("4. 按 Ctrl+C 停止机器人")
        print("-" * 50)
        
    async def on_group_at_message_create(self, message):
        """当机器人在群聊中被@时触发"""
        print(f"📨 收到群聊@消息: {message.content}")
        print(f"👤 发送者: {message.author.username}")
        
        # 简单的回复逻辑
        if "测试" in message.content:
            await message.reply(content="✅ 测试成功！机器人运行正常！")
            print("✅ 已回复测试消息")
        elif "你好" in message.content:
            await message.reply(content="你好！我是QQ机器人，很高兴为你服务！")
            print("👋 已回复问候消息")
        else:
            await message.reply(content="我收到了你的消息！发送'测试'来验证机器人功能。")
            print("💬 已回复通用消息")
    
    async def on_c2c_message_create(self, message):
        """当收到私聊消息时触发"""
        print(f"📩 收到私聊消息: {message.content}")
        await message.reply(content="收到私聊消息，机器人运行正常！")
        print("✅ 已回复私聊消息")


async def test_main():
    """测试主函数"""
    print("🧪 QQ机器人测试模式")
    print("=" * 50)
    
    # 使用默认的 intents
    intents = botpy.Intents.default()
    
    client = TestClient(intents=intents, is_sandbox=config.IS_SANDBOX)
    
    try:
        # 启动机器人
        await client.start(appid=config.BOT_ID, secret=config.BOT_SECRET)
    except Exception as e:
        print(f"❌ 机器人启动失败: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(test_main())
    except KeyboardInterrupt:
        print("\n👋 测试结束，机器人已停止")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
