#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI智能机器人+抽奖系统启动脚本
提供友好的启动界面和配置检查
"""

import sys
import asyncio
import traceback
from ai_lottery_bot import main
import config

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════╗
    ║         AI智能机器人 + 抽奖系统启动器             ║
    ╠══════════════════════════════════════════════════╣
    ║  机器人QQ号: {:<35} ║
    ║  机器人ID:   {:<35} ║
    ║  沙箱模式:   {:<35} ║
    ║  AI功能:     {:<35} ║
    ║  抽奖功能:   {:<35} ║
    ╚══════════════════════════════════════════════════╝
    """.format(
        config.BOT_QQ,
        config.BOT_ID,
        "是" if config.IS_SANDBOX else "否",
        "已启用" if getattr(config, 'AI_ENABLED', True) and config.OPENAI_API_KEY != "your-openai-api-key-here" else "未配置",
        "已启用" if getattr(config, 'LOTTERY_ENABLED', False) else "未启用"
    )
    print(banner)

def check_config():
    """检查配置是否完整"""
    print("🔍 正在检查配置...")
    
    # 检查基础配置
    required_configs = [
        ('BOT_QQ', config.BOT_QQ),
        ('BOT_ID', config.BOT_ID),
        ('BOT_TOKEN', config.BOT_TOKEN),
        ('BOT_SECRET', config.BOT_SECRET)
    ]
    
    missing_configs = []
    for name, value in required_configs:
        if not value or value == "":
            missing_configs.append(name)
    
    if missing_configs:
        print("❌ 基础配置检查失败！")
        print(f"缺少以下配置项: {', '.join(missing_configs)}")
        return False
    
    print("✅ 基础配置检查通过")
    
    # 检查AI配置
    ai_enabled = getattr(config, 'AI_ENABLED', True)
    if ai_enabled:
        if hasattr(config, 'OPENAI_API_KEY') and config.OPENAI_API_KEY != "your-openai-api-key-here":
            print("✅ AI功能配置完整")
        else:
            print("⚠️ AI功能未配置（将使用基础回复功能）")
    else:
        print("ℹ️ AI功能已禁用")
    
    # 检查抽奖配置
    lottery_enabled = getattr(config, 'LOTTERY_ENABLED', False)
    if lottery_enabled:
        lottery_admins = getattr(config, 'LOTTERY_ADMINS', [])
        if lottery_admins:
            print(f"✅ 抽奖功能已启用，管理员数量: {len(lottery_admins)}")
        else:
            print("⚠️ 抽奖功能已启用但未配置管理员")
    else:
        print("ℹ️ 抽奖功能已禁用")
    
    return True

def show_features():
    """显示功能说明"""
    print("\n🎯 功能说明:")
    print("=" * 50)
    
    # AI功能
    ai_enabled = getattr(config, 'AI_ENABLED', True) and hasattr(config, 'OPENAI_API_KEY') and config.OPENAI_API_KEY != "your-openai-api-key-here"
    if ai_enabled:
        print("🤖 AI智能对话:")
        print("   • @机器人 + 任意消息 - AI智能回复")
        print("   • 支持上下文对话记忆")
        print("   • 自动过滤不安全内容")
    
    # 抽奖功能
    lottery_enabled = getattr(config, 'LOTTERY_ENABLED', False)
    if lottery_enabled:
        print("\n🎲 抽奖系统:")
        print("   • 自动记录群聊消息")
        print("   • 管理员可创建抽奖活动")
        print("   • 支持灵活的时间设置")
        print("   • 公平随机抽奖算法")
    
    # 基础功能
    print("\n📝 基础指令:")
    print("   • @机器人 帮助 - 查看完整帮助")
    print("   • @机器人 状态 - 查看机器人状态")
    print("   • @机器人 时间 - 查看当前时间")
    
    if lottery_enabled:
        print("   • @机器人 抽奖帮助 - 查看抽奖功能")

def show_usage_tips():
    """显示使用提示"""
    print("\n💡 使用提示:")
    print("=" * 50)
    
    lottery_enabled = getattr(config, 'LOTTERY_ENABLED', False)
    if lottery_enabled:
        lottery_admins = getattr(config, 'LOTTERY_ADMINS', [])
        if lottery_admins:
            print("🎲 抽奖功能快速开始:")
            print("   1. 将机器人添加到群聊")
            print("   2. 管理员发送: @机器人 创建抽奖 7 2小时后")
            print("   3. 用户发送: @机器人 抽奖状态")
            print("   4. 到时间自动开奖或管理员手动开奖")
        else:
            print("⚠️ 抽奖功能提示:")
            print("   请在config.py中配置LOTTERY_ADMINS添加管理员")
    
    ai_enabled = getattr(config, 'AI_ENABLED', True) and hasattr(config, 'OPENAI_API_KEY') and config.OPENAI_API_KEY != "your-openai-api-key-here"
    if ai_enabled:
        print("\n🤖 AI对话快速开始:")
        print("   1. 在群聊中@机器人")
        print("   2. 发送任意消息进行AI对话")
        print("   3. 支持连续对话和上下文记忆")

async def start_bot():
    """启动机器人"""
    try:
        print("\n🚀 正在启动机器人...")
        await main()
    except KeyboardInterrupt:
        print("\n👋 机器人已停止运行")
    except Exception as e:
        print(f"\n❌ 机器人运行出错: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return False
    return True

if __name__ == "__main__":
    print_banner()
    
    # 检查配置
    if not check_config():
        print("\n❌ 配置检查失败，请检查config.py文件")
        sys.exit(1)
    
    # 显示功能说明
    show_features()
    
    # 显示使用提示
    show_usage_tips()
    
    print("\n" + "=" * 50)
    print("🎉 准备就绪！按 Ctrl+C 停止机器人")
    print("=" * 50)
    
    # 启动机器人
    try:
        asyncio.run(start_bot())
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)
