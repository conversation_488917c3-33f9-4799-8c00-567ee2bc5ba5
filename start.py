#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QQ机器人启动脚本
提供更友好的启动界面和错误处理
"""

import sys
import asyncio
import traceback
from bot import main
import config

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════╗
    ║            QQ机器人启动器             ║
    ╠══════════════════════════════════════╣
    ║  机器人QQ号: {:<23} ║
    ║  机器人ID:   {:<23} ║
    ║  沙箱模式:   {:<23} ║
    ╚══════════════════════════════════════╝
    """.format(
        config.BOT_QQ,
        config.BOT_ID,
        "是" if config.IS_SANDBOX else "否"
    )
    print(banner)

def check_config():
    """检查配置是否完整"""
    required_configs = [
        ('BOT_QQ', config.BOT_QQ),
        ('BOT_ID', config.BOT_ID),
        ('BOT_TOKEN', config.BOT_TOKEN),
        ('BOT_SECRET', config.BOT_SECRET)
    ]
    
    missing_configs = []
    for name, value in required_configs:
        if not value or value == "":
            missing_configs.append(name)
    
    if missing_configs:
        print("❌ 配置检查失败！")
        print(f"缺少以下配置项: {', '.join(missing_configs)}")
        print("请检查 config.py 文件中的配置")
        return False
    
    print("✅ 配置检查通过")
    return True

async def start_bot():
    """启动机器人"""
    try:
        print("🚀 正在启动机器人...")
        await main()
    except KeyboardInterrupt:
        print("\n👋 机器人已停止运行")
    except Exception as e:
        print(f"❌ 机器人运行出错: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        return False
    return True

if __name__ == "__main__":
    print_banner()
    
    # 检查配置
    if not check_config():
        sys.exit(1)
    
    # 启动机器人
    try:
        asyncio.run(start_bot())
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)
