#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示版AI QQ机器人
用于展示AI功能，使用模拟回复而不需要真实的OpenAI API Key
"""

import asyncio
import botpy
from botpy import logging
import config
import datetime
import random

# 设置日志级别
_log = logging.get_logger()


class MockAIAssistant:
    """模拟AI助手，用于演示"""
    
    def __init__(self):
        self.conversation_cache = {}
        
        # 预设的AI回复模板
        self.ai_responses = [
            "这是一个很有趣的问题！作为AI助手，我认为{topic}确实值得深入探讨。",
            "根据我的理解，{topic}是一个复杂的话题。让我为你详细解释一下...",
            "你提到的{topic}让我想到了很多相关的内容。简单来说...",
            "关于{topic}，我可以从几个角度来分析：首先...",
            "这是个好问题！{topic}确实是现在很热门的话题。",
            "我理解你对{topic}的关注。让我用简单的话来解释...",
            "哇，{topic}真是个有意思的话题！我来分享一些我的想法...",
            "你问的{topic}正好是我擅长的领域！让我来帮你分析一下..."
        ]
        
        # 特殊关键词回复
        self.special_responses = {
            "你好": "你好！我是AI智能助手，很高兴为你服务！有什么我可以帮助你的吗？",
            "介绍": "我是一个基于大语言模型的AI助手，可以回答各种问题、进行对话、协助思考等。虽然这是演示版本，但我会尽力为你提供有用的回复！",
            "天气": "抱歉，我无法获取实时天气信息。建议你查看天气预报应用或网站获取准确的天气信息。",
            "时间": f"当前时间是 {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "诗": "春风又绿江南岸，\n明月何时照我还。\n这是王安石的名句，\n你想听更多诗词吗？",
            "笑话": "为什么程序员喜欢黑暗？\n因为光明会产生bug！😄",
            "故事": "从前有一个程序员，他写了一个Hello World程序，结果改变了整个世界...这就是编程的魅力！"
        }
    
    def is_enabled(self):
        """模拟AI功能启用状态"""
        return True
    
    async def get_ai_response(self, user_message: str, user_id: str = "default") -> str:
        """
        模拟AI回复生成
        """
        # 模拟思考时间
        await asyncio.sleep(1)
        
        user_message_lower = user_message.lower()
        
        # 检查特殊关键词
        for keyword, response in self.special_responses.items():
            if keyword in user_message_lower:
                return f"🤖 {response}"
        
        # 提取话题（简单实现）
        topic = "这个话题"
        if len(user_message) > 10:
            # 尝试提取主要内容作为话题
            words = user_message.split()
            if len(words) > 2:
                topic = words[1] if len(words[1]) > 1 else words[0]
        
        # 随机选择回复模板
        template = random.choice(self.ai_responses)
        response = template.format(topic=topic)
        
        # 添加一些随机的补充内容
        supplements = [
            "希望这个回答对你有帮助！",
            "如果你还有其他问题，随时可以问我。",
            "这只是我的一个观点，你觉得呢？",
            "你还想了解更多相关内容吗？",
            "我很乐意继续和你讨论这个话题。"
        ]
        
        if random.random() > 0.3:  # 70%概率添加补充
            response += " " + random.choice(supplements)
        
        return f"🤖 {response}"
    
    def clear_conversation_history(self, user_id: str):
        """清除对话历史"""
        if user_id in self.conversation_cache:
            del self.conversation_cache[user_id]
    
    def get_status(self):
        """获取状态信息"""
        return {
            "enabled": True,
            "model": "demo-ai-model",
            "api_base": "demo://localhost",
            "max_tokens": 500,
            "temperature": 0.7,
            "cached_conversations": len(self.conversation_cache)
        }


class DemoAIBotClient(botpy.Client):
    """演示版AI机器人客户端"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ai_assistant = MockAIAssistant()
        
        # 预定义指令列表
        self.predefined_commands = [
            "帮助", "help", "时间", "time", "ping", "状态", "status",
            "清除历史", "clear", "ai状态", "ai_status"
        ]
    
    async def on_ready(self):
        """当机器人启动完成时触发"""
        print(f"✅ 演示版AI机器人 {self.robot.name} 已启动!")
        print(f"📱 机器人ID: {self.robot.id}")
        print("🤖 AI功能已启用（演示模式）")
        print("   模型: demo-ai-model")
        print("   状态: 模拟AI回复")
        print("🎯 机器人正在等待消息...")
        print("\n📋 演示功能:")
        print("• @机器人 + 任意消息 - 模拟AI智能回复")
        print("• @机器人 + 预定义指令 - 执行特定功能")
        print("• 支持关键词识别（你好、介绍、天气、诗、笑话等）")
        print("-" * 50)
    
    def _is_predefined_command(self, content: str) -> bool:
        """检查是否为预定义指令"""
        content_lower = content.lower().strip()
        return any(cmd in content_lower for cmd in self.predefined_commands)
    
    async def _handle_predefined_command(self, message, content: str):
        """处理预定义指令"""
        content_lower = content.lower().strip()
        
        if "帮助" in content_lower or "help" in content_lower:
            help_text = """
🤖 演示版AI机器人帮助：

📝 基础指令：
• 帮助/help - 显示此帮助信息
• 时间/time - 显示当前时间
• ping - 测试机器人响应
• 状态/status - 显示机器人状态
• ai状态 - 显示AI功能状态
• 清除历史/clear - 清除对话历史

🧠 AI演示功能：
• 直接@我说话 - 模拟AI智能回复
• 支持关键词：你好、介绍、天气、诗、笑话、故事
• 模拟上下文对话

💡 这是演示版本，使用模拟AI回复
            """
            await message.reply(content=help_text)
            
        elif "时间" in content_lower or "time" in content_lower:
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await message.reply(content=f"⏰ 当前时间: {now}")
            
        elif "ping" in content_lower:
            await message.reply(content="pong! 🏓 演示版AI机器人运行正常")
            
        elif "状态" in content_lower or "status" in content_lower:
            status_text = f"""
📊 演示版机器人状态：
• 机器人名称: {self.robot.name}
• 机器人ID: {self.robot.id}
• AI功能: ✅ 已启用（演示模式）
• 运行状态: ✅ 正常
            """
            await message.reply(content=status_text)
            
        elif "ai状态" in content_lower:
            status = self.ai_assistant.get_status()
            ai_status_text = f"""
🤖 AI演示功能状态：
• 状态: ✅ 已启用（演示模式）
• 模型: {status['model']}
• API地址: {status['api_base']}
• 缓存对话数: {status['cached_conversations']}
• 说明: 使用模拟AI回复，无需真实API Key
            """
            await message.reply(content=ai_status_text)
            
        elif "清除历史" in content_lower or "clear" in content_lower:
            user_id = str(message.author.id)
            self.ai_assistant.clear_conversation_history(user_id)
            await message.reply(content="✅ 已清除你的演示对话历史")
    
    async def _handle_ai_message(self, message, content: str):
        """处理AI消息（演示版）"""
        # 显示正在思考的提示
        await message.reply(content="🤔 AI正在思考中...")
        
        try:
            user_id = str(message.author.id)
            
            # 获取模拟AI回复
            ai_response = await self.ai_assistant.get_ai_response(content, user_id)
            
            if ai_response:
                await message.reply(content=ai_response)
                print(f"✅ 演示AI回复: {content[:30]}... -> {ai_response[:50]}...")
                _log.info(f"演示AI回复成功: 用户{user_id}")
            else:
                await message.reply(content="❌ 演示AI回复生成失败")
                
        except Exception as e:
            await message.reply(content="❌ 处理演示消息时出现错误")
            print(f"❌ 处理演示AI消息时出错: {e}")
    
    async def on_group_at_message_create(self, message):
        """当机器人在群聊中被@时触发"""
        content = message.content.strip()
        print(f"📨 收到群聊@消息: {content}")
        _log.info(f"收到群聊@消息: {content}")
        
        if self._is_predefined_command(content):
            await self._handle_predefined_command(message, content)
        else:
            await self._handle_ai_message(message, content)
    
    async def on_c2c_message_create(self, message):
        """当收到私聊消息时触发"""
        content = message.content.strip()
        print(f"📩 收到私聊消息: {content}")
        _log.info(f"收到私聊消息: {content}")
        
        if self._is_predefined_command(content):
            await self._handle_predefined_command(message, content)
        else:
            await self._handle_ai_message(message, content)


async def main():
    """主函数"""
    print("🚀 演示版AI机器人启动中...")
    print("=" * 50)
    
    intents = botpy.Intents.default()
    client = DemoAIBotClient(intents=intents, is_sandbox=config.IS_SANDBOX)
    
    try:
        await client.start(appid=config.BOT_ID, secret=config.BOT_SECRET)
    except Exception as e:
        print(f"❌ 演示版AI机器人启动失败: {e}")
        _log.error(f"演示版AI机器人启动失败: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示版AI机器人已停止")
    except Exception as e:
        print(f"❌ 演示版AI机器人运行失败: {e}")
