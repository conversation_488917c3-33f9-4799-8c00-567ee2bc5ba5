# QQ机器人配置文件模板
# 复制此文件为 config.py 并填入你的机器人信息

# 机器人基本信息
BOT_QQ = "你的机器人QQ号"      # 机器人QQ号
BOT_ID = "你的机器人ID"        # 机器人ID
BOT_TOKEN = "你的机器人令牌"    # 机器人令牌
BOT_SECRET = "你的机器人密钥"   # 机器人密钥

# 是否为沙箱环境
IS_SANDBOX = True  # 开发测试时设为True，正式上线时设为False

# OpenAI大模型配置
OPENAI_API_KEY = "your-openai-api-key-here"  # 请替换为你的OpenAI API Key
OPENAI_API_BASE = "https://api.openai.com/v1"  # OpenAI API地址，可以替换为其他兼容的API地址
OPENAI_MODEL = "gpt-3.5-turbo"  # 使用的模型，可选: gpt-3.5-turbo, gpt-4, gpt-4-turbo等

# AI回复配置
AI_ENABLED = True  # 是否启用AI回复功能
AI_MAX_TOKENS = 500  # AI回复的最大token数
AI_TEMPERATURE = 0.7  # AI回复的创造性程度 (0-1)
AI_SYSTEM_PROMPT = "你是一个友好的QQ群聊机器人助手，请用简洁、有趣的方式回复用户的消息。回复长度控制在100字以内。"
