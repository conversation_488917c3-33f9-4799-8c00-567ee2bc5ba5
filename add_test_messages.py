#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
添加测试消息到抽奖系统
用于测试抽奖功能
"""

import time
from lottery_system import lottery_system

def add_test_messages():
    """添加一些测试消息"""
    print("🧪 添加测试消息到抽奖系统...")
    
    # 你的群组ID（从机器人日志中获取）
    group_id = "7E8B88D5C788D5B7A61DBCCCA0E25325"  # 替换为实际的群组ID
    
    # 测试消息
    test_messages = [
        ("user_001", "今天天气真不错！"),
        ("user_002", "大家好，我是新人"),
        ("user_003", "有人一起打游戏吗？"),
        ("user_004", "晚上吃什么好呢？"),
        ("user_005", "推荐一个好电影"),
        ("user_006", "周末有什么计划？"),
        ("user_007", "这个群好热闹啊"),
        ("user_008", "学习使我快乐"),
        ("user_009", "今天工作好累"),
        ("user_010", "明天是个好日子"),
    ]
    
    # 添加消息
    added_count = 0
    for user_id, content in test_messages:
        if lottery_system.record_message(group_id, user_id, content):
            added_count += 1
            print(f"✅ 添加消息: {user_id} - {content}")
        else:
            print(f"⚠️ 消息已存在: {user_id} - {content}")
    
    print(f"\n📊 总共添加了 {added_count} 条测试消息")
    print("🎉 现在可以测试抽奖功能了！")

if __name__ == "__main__":
    add_test_messages()
