2025-08-06 22:16:56,611	[ERROR]	(bot.py:80)main	机器人启动失败: Client.start() got an unexpected keyword argument 'token'
2025-08-06 22:17:14,857	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:17:15,004	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 262
2025-08-06 22:17:15,596	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:17:15,597	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:15,597	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:15,597	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:15,722	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:15,785	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:15,785	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:15,786	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:17:20,784	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:20,785	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:20,785	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:20,904	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:20,945	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:20,945	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:20,945	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:17:25,940	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:25,940	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:25,940	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:26,069	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:26,135	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:26,136	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:26,136	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:17:31,144	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:31,144	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:31,144	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:31,266	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:31,306	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:31,306	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:31,306	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:17:36,315	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:36,315	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:36,316	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:36,429	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:36,468	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:36,469	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:36,469	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:18:26,958	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:18:27,992	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 189
2025-08-06 22:18:28,428	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:18:28,428	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:18:28,429	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:18:28,429	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:18:28,542	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:18:28,628	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:18:28,629	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:18:28,629	[INFO]	(bot.py:18)on_ready	机器人 早鸟-测试中 已启动!
2025-08-06 22:18:28,629	[INFO]	(bot.py:19)on_ready	机器人ID: 1411261171501187073
2025-08-06 22:23:07,793	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:23:07,930	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 7200
2025-08-06 22:23:08,426	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:23:08,426	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:23:08,426	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:23:08,426	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:23:08,546	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:23:08,663	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:23:08,663	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:26:23,844	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:26:23,976	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 7004
2025-08-06 22:26:24,088	[ERROR]	(hello_bot.py:110)main	Hello机器人启动失败: Cannot connect to host sandbox.api.sgroup.qq.com:443 ssl:default [getaddrinfo failed]
2025-08-06 22:27:31,948	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:27:33,307	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 6935
2025-08-06 22:27:33,477	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/users/@me, 错误代码: 401, 返回内容: {'message': '接口访问源IP不在白名单', 'code': 11298, 'err_code': 40023002, 'trace_id': '1bd7c45b725d5a4199402742792c74a4'}, trace_id:1bd7c45b725d5a4199402742792c74a4
2025-08-06 22:27:33,477	[ERROR]	(hello_bot.py:110)main	Hello机器人启动失败: 接口访问源IP不在白名单
2025-08-06 22:28:49,683	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:28:49,804	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 6858
2025-08-06 22:28:51,008	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:28:51,008	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:28:51,008	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:28:51,008	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:28:51,118	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:28:51,206	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:28:51,206	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:29:15,815	[INFO]	(hello_bot.py:54)on_group_at_message_create	收到群聊@消息:  发送hello
2025-08-06 22:29:16,004	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '主动消息失败, 无权限', 'code': 40034102, 'err_code': 40034102, 'trace_id': '5089a98429553195efe7c6651972a7a7'}, trace_id:5089a98429553195efe7c6651972a7a7
2025-08-06 22:29:17,486	[ERROR]	(hello_bot.py:71)on_group_at_message_create	发送hello消息失败: 主动消息失败, 无权限
2025-08-06 22:31:13,590	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:31:13,721	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 6714
2025-08-06 22:31:14,157	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:31:14,157	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:31:14,158	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:31:14,158	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:31:14,274	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:31:14,351	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:31:14,351	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:31:58,016	[INFO]	(hello_bot.py:54)on_group_at_message_create	收到群聊@消息:  发送hello
2025-08-06 22:31:58,040	[INFO]	(simple_hello_bot.py:36)on_group_at_message_create	收到群聊@消息:  发送hello
2025-08-06 22:31:58,217	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '主动消息失败, 无权限', 'code': 40034102, 'err_code': 40034102, 'trace_id': '14d5bfb659950ab5119f49fc91c112b2'}, trace_id:14d5bfb659950ab5119f49fc91c112b2
2025-08-06 22:31:58,936	[ERROR]	(hello_bot.py:71)on_group_at_message_create	发送hello消息失败: 主动消息失败, 无权限
2025-08-06 22:31:59,276	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '消息被去重，请检查请求msgseq', 'code': 40054005, 'err_code': 40054005, 'trace_id': '309e69141004ef722a1d6db8f727afcb'}, trace_id:309e69141004ef722a1d6db8f727afcb
2025-08-06 22:31:59,276	[ERROR]	(simple_hello_bot.py:49)on_group_at_message_create	回复hello消息失败: 消息被去重，请检查请求msgseq
2025-08-06 22:33:00,070	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:33:01,439	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 6606
2025-08-06 22:33:02,270	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:33:02,270	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:33:02,270	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:33:02,270	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:33:02,404	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:33:02,490	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:33:02,490	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:33:21,067	[INFO]	(simple_hello_bot.py:36)on_group_at_message_create	收到群聊@消息:  发送hello
2025-08-06 22:33:22,861	[INFO]	(simple_hello_bot.py:46)on_group_at_message_create	已回复hello消息
2025-08-06 22:42:13,249	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:42:15,031	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 6053
2025-08-06 22:42:15,867	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:42:15,867	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:42:15,868	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:42:15,868	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:42:15,972	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:42:16,056	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:42:16,056	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:43:31,452	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:43:31,613	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 5976
2025-08-06 22:43:32,107	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:43:32,108	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:43:32,108	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:43:32,108	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:43:32,228	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:43:32,310	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:43:32,311	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:43:51,902	[INFO]	(ai_bot.py:159)on_group_at_message_create	收到群聊@消息: 你好 你是什么模型？
2025-08-06 22:43:53,297	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '消息被去重，请检查请求msgseq', 'code': 40054005, 'err_code': 40054005, 'trace_id': '114dbe0997ea1817742b56e11f49c306'}, trace_id:114dbe0997ea1817742b56e11f49c306
2025-08-06 22:47:23,628	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:47:23,763	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 5744
2025-08-06 22:47:24,672	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:47:24,672	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:47:24,673	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:47:24,673	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:47:24,798	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:47:24,956	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:47:24,956	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:47:36,052	[INFO]	(ai_bot.py:180)on_group_at_message_create	收到群聊@消息: 你是什么模型
2025-08-06 22:47:37,375	[ERROR]	(ai_bot.py:174)_handle_ai_message	处理AI消息时出错: '_User' object has no attribute 'id'
2025-08-06 22:51:34,728	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:51:35,248	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 5493
2025-08-06 22:51:35,716	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:51:35,716	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:51:35,718	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:51:35,718	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:51:35,839	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:51:35,921	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:51:35,922	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:51:45,462	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 你是什么模型
2025-08-06 22:51:50,122	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 22:52:23,632	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 3+2-5*0等于多少
2025-08-06 22:52:27,051	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 22:53:27,427	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 你爸爸是谁？
2025-08-06 22:53:31,282	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 22:56:29,742	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 编写一个python画贪吃蛇的代码
2025-08-06 22:56:40,626	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 22:57:00,866	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 画只小鸟
2025-08-06 22:57:05,722	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '消息发送失败, 不允许发送url t.circle,t.circle', 'code': 40054010, 'err_code': 40054010, 'trace_id': 'e191a930b79faf1e089230eaefece294'}, trace_id:e191a930b79faf1e089230eaefece294
2025-08-06 22:57:06,539	[ERROR]	(ai_bot.py:188)_handle_ai_message	处理AI消息时出错: 消息发送失败, 不允许发送url t.circle,t.circle
2025-08-06 23:02:43,155	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 23:02:44,355	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 4823
2025-08-06 23:02:44,805	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 23:02:44,805	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 23:02:44,805	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 23:02:44,806	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 23:02:44,927	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 23:02:45,024	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 23:02:45,025	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 23:02:54,577	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 画只大象
2025-08-06 23:02:57,882	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 23:16:41,722	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 23:16:42,514	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 3985
2025-08-06 23:16:42,969	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 23:16:42,969	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 23:16:42,969	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 23:16:42,970	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 23:16:43,090	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 23:16:43,187	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 23:16:43,188	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 23:16:59,256	[INFO]	(ai_lottery_bot.py:336)on_group_at_message_create	收到群聊@消息: 抽奖
2025-08-06 23:17:05,975	[INFO]	(ai_lottery_bot.py:318)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 23:17:52,514	[INFO]	(ai_lottery_bot.py:336)on_group_at_message_create	收到群聊@消息: 抽奖帮助
2025-08-06 23:21:44,027	[INFO]	(ai_lottery_bot.py:336)on_group_at_message_create	收到群聊@消息: 创建抽奖 1 1小时后
2025-08-06 23:23:02,348	[INFO]	(ai_lottery_bot.py:336)on_group_at_message_create	收到群聊@消息: 添加管理员
2025-08-06 23:27:09,701	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 23:27:11,357	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 3356
2025-08-06 23:27:11,801	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 23:27:11,801	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 23:27:11,802	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 23:27:11,802	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 23:27:11,932	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 23:27:12,040	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 23:27:12,040	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 23:27:42,193	[INFO]	(ai_lottery_bot.py:384)on_group_at_message_create	收到群聊@消息: 添加管理员
2025-08-06 23:29:05,693	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 23:29:07,366	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 3240
2025-08-06 23:29:08,624	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 23:29:08,624	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 23:29:08,625	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 23:29:08,625	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 23:29:08,738	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 23:29:08,872	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 23:29:08,872	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 23:29:28,960	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 我的id
2025-08-06 23:29:34,266	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 23:29:50,563	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 创建抽奖 7 1小时后
2025-08-06 23:29:55,649	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 23:30:10,154	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 创建抽奖 7 1小时后
2025-08-06 23:30:14,989	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 23:30:43,283	[INFO]	(ai_bot.py:194)on_group_at_message_create	收到群聊@消息: 添加管理员
2025-08-06 23:30:49,540	[INFO]	(ai_bot.py:176)_handle_ai_message	AI回复成功: 用户8CC91A5198960CE8A98498065FA79F87
2025-08-06 23:31:44,006	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 23:31:44,139	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 3084
2025-08-06 23:31:44,563	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 23:31:44,564	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 23:31:44,564	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 23:31:44,564	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 23:31:44,696	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 23:31:44,783	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 23:31:44,783	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 23:32:02,364	[INFO]	(ai_lottery_bot.py:397)on_group_at_message_create	收到群聊@消息: 我的id
2025-08-06 23:32:03,544	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '消息发送失败, 不允许发送url config.py', 'code': 40054010, 'err_code': 40054010, 'trace_id': 'a924f0ac2a61285fc874e6c8d8af9f40'}, trace_id:a924f0ac2a61285fc874e6c8d8af9f40
2025-08-06 23:32:24,457	[INFO]	(ai_lottery_bot.py:397)on_group_at_message_create	收到群聊@消息: 添加管理员
2025-08-06 23:33:50,912	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 23:33:51,288	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 2956
2025-08-06 23:33:52,141	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 23:33:52,141	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 23:33:52,141	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 23:33:52,141	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 23:33:52,255	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 23:33:52,329	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 23:33:52,329	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 23:34:11,910	[INFO]	(ai_lottery_bot.py:407)on_group_at_message_create	收到群聊@消息: 创建抽奖 7 1小时后
2025-08-06 23:35:42,317	[INFO]	(ai_lottery_bot.py:407)on_group_at_message_create	收到群聊@消息: 我的id
