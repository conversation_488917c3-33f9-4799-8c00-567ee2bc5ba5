2025-08-06 22:16:56,611	[ERROR]	(bot.py:80)main	机器人启动失败: Client.start() got an unexpected keyword argument 'token'
2025-08-06 22:17:14,857	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:17:15,004	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 262
2025-08-06 22:17:15,596	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:17:15,597	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:15,597	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:15,597	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:15,722	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:15,785	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:15,785	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:15,786	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:17:20,784	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:20,785	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:20,785	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:20,904	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:20,945	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:20,945	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:20,945	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:17:25,940	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:25,940	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:25,940	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:26,069	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:26,135	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:26,136	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:26,136	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:17:31,144	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:31,144	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:31,144	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:31,266	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:31,306	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:31,306	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:31,306	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:17:36,315	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:17:36,315	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:17:36,316	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:17:36,429	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:17:36,468	[INFO]	(gateway.py:56)on_closed	[botpy] 关闭, 返回码: 4014, 返回信息: disallowed intents
2025-08-06 22:17:36,469	[INFO]	(gateway.py:62)on_closed	[botpy] 无法重连，创建新连接!
2025-08-06 22:17:36,469	[INFO]	(gateway.py:134)ws_connect	[botpy] ws关闭, 停止接收消息!
2025-08-06 22:18:26,958	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:18:27,992	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 189
2025-08-06 22:18:28,428	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:18:28,428	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:18:28,429	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:18:28,429	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:18:28,542	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:18:28,628	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:18:28,629	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:18:28,629	[INFO]	(bot.py:18)on_ready	机器人 早鸟-测试中 已启动!
2025-08-06 22:18:28,629	[INFO]	(bot.py:19)on_ready	机器人ID: 1411261171501187073
2025-08-06 22:23:07,793	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:23:07,930	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 7200
2025-08-06 22:23:08,426	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:23:08,426	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:23:08,426	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:23:08,426	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:23:08,546	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:23:08,663	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:23:08,663	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:26:23,844	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:26:23,976	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 7004
2025-08-06 22:26:24,088	[ERROR]	(hello_bot.py:110)main	Hello机器人启动失败: Cannot connect to host sandbox.api.sgroup.qq.com:443 ssl:default [getaddrinfo failed]
2025-08-06 22:27:31,948	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:27:33,307	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 6935
2025-08-06 22:27:33,477	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/users/@me, 错误代码: 401, 返回内容: {'message': '接口访问源IP不在白名单', 'code': 11298, 'err_code': 40023002, 'trace_id': '1bd7c45b725d5a4199402742792c74a4'}, trace_id:1bd7c45b725d5a4199402742792c74a4
2025-08-06 22:27:33,477	[ERROR]	(hello_bot.py:110)main	Hello机器人启动失败: 接口访问源IP不在白名单
2025-08-06 22:28:49,683	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:28:49,804	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 6858
2025-08-06 22:28:51,008	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:28:51,008	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:28:51,008	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:28:51,008	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:28:51,118	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:28:51,206	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:28:51,206	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:29:15,815	[INFO]	(hello_bot.py:54)on_group_at_message_create	收到群聊@消息:  发送hello
2025-08-06 22:29:16,004	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '主动消息失败, 无权限', 'code': 40034102, 'err_code': 40034102, 'trace_id': '5089a98429553195efe7c6651972a7a7'}, trace_id:5089a98429553195efe7c6651972a7a7
2025-08-06 22:29:17,486	[ERROR]	(hello_bot.py:71)on_group_at_message_create	发送hello消息失败: 主动消息失败, 无权限
2025-08-06 22:31:13,590	[INFO]	(client.py:162)_bot_login	[botpy] 登录机器人账号中...
2025-08-06 22:31:13,721	[INFO]	(robot.py:65)update_access_token	[botpy] access_token expires_in 6714
2025-08-06 22:31:14,157	[INFO]	(client.py:181)_bot_init	[botpy] 程序启动...
2025-08-06 22:31:14,157	[INFO]	(connection.py:60)multi_run	[botpy] 最大并发连接数: 1, 启动会话数: 1
2025-08-06 22:31:14,158	[INFO]	(client.py:242)bot_connect	[botpy] 会话启动中...
2025-08-06 22:31:14,158	[INFO]	(gateway.py:115)ws_connect	[botpy] 启动中...
2025-08-06 22:31:14,274	[INFO]	(gateway.py:142)ws_identify	[botpy] 鉴权中...
2025-08-06 22:31:14,351	[INFO]	(gateway.py:85)on_message	[botpy] 机器人「早鸟-测试中」启动成功！
2025-08-06 22:31:14,351	[INFO]	(gateway.py:223)_send_heart	[botpy] 心跳维持启动...
2025-08-06 22:31:58,016	[INFO]	(hello_bot.py:54)on_group_at_message_create	收到群聊@消息:  发送hello
2025-08-06 22:31:58,040	[INFO]	(simple_hello_bot.py:36)on_group_at_message_create	收到群聊@消息:  发送hello
2025-08-06 22:31:58,217	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '主动消息失败, 无权限', 'code': 40034102, 'err_code': 40034102, 'trace_id': '14d5bfb659950ab5119f49fc91c112b2'}, trace_id:14d5bfb659950ab5119f49fc91c112b2
2025-08-06 22:31:58,936	[ERROR]	(hello_bot.py:71)on_group_at_message_create	发送hello消息失败: 主动消息失败, 无权限
2025-08-06 22:31:59,276	[ERROR]	(http.py:76)_handle_response	[botpy] 接口请求异常，请求连接: https://api.sgroup.qq.com/v2/groups/7E8B88D5C788D5B7A61DBCCCA0E25325/messages, 错误代码: 400, 返回内容: {'message': '消息被去重，请检查请求msgseq', 'code': 40054005, 'err_code': 40054005, 'trace_id': '309e69141004ef722a1d6db8f727afcb'}, trace_id:309e69141004ef722a1d6db8f727afcb
2025-08-06 22:31:59,276	[ERROR]	(simple_hello_bot.py:49)on_group_at_message_create	回复hello消息失败: 消息被去重，请检查请求msgseq
