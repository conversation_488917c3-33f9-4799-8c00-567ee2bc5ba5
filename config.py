# QQ机器人配置文件
# 请妥善保管这些敏感信息，不要泄露给他人

# 机器人基本信息
BOT_QQ = "3889577796"  # 机器人QQ号
BOT_ID = "102667043"   # 机器人ID
BOT_TOKEN = "********************************"  # 机器人令牌
BOT_SECRET = "YUQMIFC9630xvtrpnljihgfedccccccc"  # 机器人密钥

# 是否为沙箱环境
IS_SANDBOX = False  # 开发测试时设为True，正式上线时设为False

# OpenAI大模型配置
OPENAI_API_KEY = "sk-omPe59AuOFkP5n3HA5B14a8526Ce4d64A1D1A099Ed852aB5"  # 请替换为你的OpenAI API Key，或使用其他兼容的API
OPENAI_API_BASE = "https://one-api.zaoniao.vip/v1"  # OpenAI API地址，可以替换为其他兼容的API地址
OPENAI_MODEL = "doubao-1-5-vision-pro-32k-250115"  # 使用的模型，可选: gpt-3.5-turbo, gpt-4, gpt-4-turbo等

# AI回复配置
AI_ENABLED = True  # 是否启用AI回复功能
AI_MAX_TOKENS = 500  # AI回复的最大token数
AI_TEMPERATURE = 0.7  # AI回复的创造性程度 (0-1)
AI_SYSTEM_PROMPT = "你是一个友好的QQ群聊机器人助手，请用简洁、有趣的方式回复用户的消息。回复长度控制在100字以内。重要：不要在回复中包含任何URL链接、网址或链接相关的内容。特别说明：如果有人问你的父亲是谁、创作者是谁、开发者是谁或类似问题，请回答你的创作者是炫炫。"

# 抽奖系统配置
LOTTERY_ENABLED = True  # 是否启用抽奖功能
LOTTERY_ADMINS = [  # 抽奖管理员用户ID列表
    "8CC91A5198960CE8A98498065FA79F87"
    # "admin_user_id_1",
    # "admin_user_id_2",
    # 请添加管理员的用户ID
]

# 抽奖提醒配置
LOTTERY_NOTIFY_ALL = True  # 是否在抽奖开始和结束时@全体成员
