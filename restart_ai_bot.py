#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI机器人重启脚本
用于快速重启AI机器人
"""

import subprocess
import sys
import os

def restart_ai_bot():
    """重启AI机器人"""
    print("🔄 正在重启AI机器人...")
    
    try:
        # 获取当前工作目录
        current_dir = os.getcwd()
        
        # 启动AI机器人
        cmd = [sys.executable, "ai_bot.py"]
        
        print(f"📂 工作目录: {current_dir}")
        print(f"🚀 执行命令: {' '.join(cmd)}")
        print("-" * 50)
        
        # 启动进程
        subprocess.run(cmd, cwd=current_dir)
        
    except KeyboardInterrupt:
        print("\n👋 AI机器人已停止")
    except Exception as e:
        print(f"❌ 重启失败: {e}")

if __name__ == "__main__":
    restart_ai_bot()
