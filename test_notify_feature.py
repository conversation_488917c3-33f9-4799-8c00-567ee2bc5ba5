#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试@全体提醒功能
"""

import config

def test_notify_settings():
    """测试提醒设置功能"""
    print("🧪 测试@全体提醒功能")
    print("=" * 50)
    
    # 测试默认设置
    print("1. 测试默认设置")
    print("-" * 30)
    default_setting = getattr(config, 'LOTTERY_NOTIFY_ALL', True)
    print(f"✅ 默认全体提醒设置: {default_setting}")
    
    # 测试设置修改
    print("\n2. 测试设置修改")
    print("-" * 30)
    
    # 开启提醒
    config.LOTTERY_NOTIFY_ALL = True
    print(f"✅ 开启全体提醒: {config.LOTTERY_NOTIFY_ALL}")
    
    # 关闭提醒
    config.LOTTERY_NOTIFY_ALL = False
    print(f"✅ 关闭全体提醒: {config.LOTTERY_NOTIFY_ALL}")
    
    # 恢复默认
    config.LOTTERY_NOTIFY_ALL = True
    print(f"✅ 恢复默认设置: {config.LOTTERY_NOTIFY_ALL}")
    
    print("\n3. 测试消息格式")
    print("-" * 30)
    
    # 测试带@everyone的消息
    at_everyone = "@everyone " if config.LOTTERY_NOTIFY_ALL else ""
    test_msg = f"🎉 抽奖活动开始啦！{at_everyone}"
    print(f"✅ 开启提醒时的消息: '{test_msg}'")
    
    # 测试不带@everyone的消息
    config.LOTTERY_NOTIFY_ALL = False
    at_everyone = "@everyone " if config.LOTTERY_NOTIFY_ALL else ""
    test_msg = f"🎉 抽奖活动开始啦！{at_everyone}"
    print(f"✅ 关闭提醒时的消息: '{test_msg}'")
    
    # 恢复设置
    config.LOTTERY_NOTIFY_ALL = True
    
    print("\n🎉 @全体提醒功能测试完成！")

def test_message_templates():
    """测试消息模板"""
    print("\n📝 测试消息模板")
    print("=" * 30)
    
    days = 7
    draw_time = "2小时后"
    
    # 开启@everyone的模板
    config.LOTTERY_NOTIFY_ALL = True
    at_everyone = "@everyone " if config.LOTTERY_NOTIFY_ALL else ""
    
    start_msg = f"""
🎉 抽奖活动开始啦！{at_everyone}

📋 活动详情：
• 选取范围: 最近{days}天的消息
• 开奖时间: {draw_time}
• 参与方式: @机器人发送任意消息即可参与

💡 参与示例：
@机器人 今天天气不错
@机器人 大家好
@机器人 我要参与抽奖

🎯 快来参与吧，好运等着你！
    """.strip()
    
    print("✅ 抽奖开始消息模板（开启@everyone）：")
    print(start_msg)
    
    # 关闭@everyone的模板
    config.LOTTERY_NOTIFY_ALL = False
    at_everyone = "@everyone " if config.LOTTERY_NOTIFY_ALL else ""
    
    start_msg_no_at = f"""
🎉 抽奖活动开始啦！{at_everyone}

📋 活动详情：
• 选取范围: 最近{days}天的消息
• 开奖时间: {draw_time}
• 参与方式: @机器人发送任意消息即可参与

💡 参与示例：
@机器人 今天天气不错
@机器人 大家好
@机器人 我要参与抽奖

🎯 快来参与吧，好运等着你！
    """.strip()
    
    print("\n✅ 抽奖开始消息模板（关闭@everyone）：")
    print(start_msg_no_at)
    
    # 恢复设置
    config.LOTTERY_NOTIFY_ALL = True

def test_result_message():
    """测试开奖结果消息"""
    print("\n🏆 测试开奖结果消息")
    print("=" * 30)
    
    # 模拟开奖数据
    winner_data = {
        "user_id": "test_user_123",
        "content": "今天天气真不错！",
        "datetime": "2024-01-01 20:00:00"
    }
    
    eligible_count = 15
    lottery_id = "group123_1704117600"
    
    # 开启@everyone的结果消息
    config.LOTTERY_NOTIFY_ALL = True
    at_everyone = "@everyone " if config.LOTTERY_NOTIFY_ALL else ""
    
    result_msg = f"""
🎉 抽奖结果公布！{at_everyone}

🏆 获奖用户: {winner_data["user_id"]}
💬 获奖消息: {winner_data["content"]}
📅 发言时间: {winner_data["datetime"]}
📊 参与消息数: {eligible_count}条
🎫 抽奖ID: {lottery_id}

🎊 恭喜获奖者！感谢大家的参与！
    """.strip()
    
    print("✅ 开奖结果消息模板（开启@everyone）：")
    print(result_msg)
    
    # 关闭@everyone的结果消息
    config.LOTTERY_NOTIFY_ALL = False
    at_everyone = "@everyone " if config.LOTTERY_NOTIFY_ALL else ""
    
    result_msg_no_at = f"""
🎉 抽奖结果公布！{at_everyone}

🏆 获奖用户: {winner_data["user_id"]}
💬 获奖消息: {winner_data["content"]}
📅 发言时间: {winner_data["datetime"]}
📊 参与消息数: {eligible_count}条
🎫 抽奖ID: {lottery_id}

🎊 恭喜获奖者！感谢大家的参与！
    """.strip()
    
    print("\n✅ 开奖结果消息模板（关闭@everyone）：")
    print(result_msg_no_at)
    
    # 恢复设置
    config.LOTTERY_NOTIFY_ALL = True

if __name__ == "__main__":
    test_notify_settings()
    test_message_templates()
    test_result_message()
    
    print("\n🎊 所有测试完成！")
    print("💡 提示：重启机器人后即可使用新的@全体提醒功能")
