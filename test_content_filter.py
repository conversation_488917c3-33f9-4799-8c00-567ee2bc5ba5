#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试内容过滤功能
"""

import re

def filter_content(content: str) -> str:
    """过滤AI回复内容，移除不被允许的URL和敏感内容"""
    if not content:
        return content
    
    # 移除常见的不被允许的URL模式
    url_patterns = [
        r'https?://[^\s]*t\.circle[^\s]*',  # t.circle相关URL
        r'https?://[^\s]*\.circle[^\s]*',   # 其他.circle域名
        r'https?://[^\s]*circle[^\s]*',     # 包含circle的URL
        r'https?://[^\s]*\.co[^\s]*',       # .co域名（可能被误判）
        r'https?://[^\s]*\.tk[^\s]*',       # .tk域名
        r'https?://[^\s]*\.ml[^\s]*',       # .ml域名
    ]
    
    filtered_content = content
    for pattern in url_patterns:
        filtered_content = re.sub(pattern, '[链接已过滤]', filtered_content, flags=re.IGNORECASE)
    
    # 移除可能的敏感词汇
    sensitive_patterns = [
        r't\.circle',
        r'circle\.com',
        r'短链接',
        r'点击链接',
    ]
    
    for pattern in sensitive_patterns:
        filtered_content = re.sub(pattern, '[内容已过滤]', filtered_content, flags=re.IGNORECASE)
    
    # 清理多余的空格和换行
    filtered_content = re.sub(r'\s+', ' ', filtered_content).strip()
    
    return filtered_content

def test_content_filter():
    """测试内容过滤功能"""
    print("🧪 测试内容过滤功能")
    print("=" * 50)
    
    test_cases = [
        # 测试用例1：包含t.circle的内容
        {
            "input": "你可以访问 https://t.circle/abc123 查看更多信息",
            "expected_filtered": True
        },
        # 测试用例2：包含其他circle域名
        {
            "input": "请点击 https://example.circle/test 链接",
            "expected_filtered": True
        },
        # 测试用例3：正常内容
        {
            "input": "这是一个正常的回复，没有任何链接",
            "expected_filtered": False
        },
        # 测试用例4：包含敏感词
        {
            "input": "你可以通过t.circle获取更多信息",
            "expected_filtered": True
        },
        # 测试用例5：混合内容
        {
            "input": "小鸟是一种可爱的动物，它们会飞翔。你可以在 https://bird.circle/info 了解更多",
            "expected_filtered": True
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        input_text = test_case["input"]
        filtered_text = filter_content(input_text)
        was_filtered = input_text != filtered_text
        
        print(f"\n测试用例 {i}:")
        print(f"原文: {input_text}")
        print(f"过滤后: {filtered_text}")
        print(f"是否被过滤: {'✅' if was_filtered else '❌'}")
        
        if test_case["expected_filtered"] == was_filtered:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
    
    print("\n🎉 内容过滤测试完成！")

if __name__ == "__main__":
    test_content_filter()
