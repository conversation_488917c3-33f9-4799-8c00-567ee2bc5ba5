#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抽奖系统模块
支持群聊消息收集和随机抽奖功能
"""

import json
import time
import random
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import os

class LotterySystem:
    """抽奖系统类"""
    
    def __init__(self, data_file: str = "lottery_data.json"):
        self.data_file = data_file
        self.data = self._load_data()
        
        # 管理员列表（可以在配置文件中设置）
        self.admins = set()  # 将在初始化时从配置加载
        
        # 活跃抽奖
        self.active_lotteries = {}  # group_id -> lottery_info
    
    def _load_data(self) -> Dict:
        """加载抽奖数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"❌ 加载抽奖数据失败: {e}")
        
        return {
            "messages": {},  # group_id -> [message_data]
            "lotteries": {},  # lottery_id -> lottery_result
            "admins": []     # admin_user_ids
        }
    
    def _save_data(self):
        """保存抽奖数据"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存抽奖数据失败: {e}")
    
    def add_admin(self, user_id: str):
        """添加管理员"""
        if user_id not in self.data["admins"]:
            self.data["admins"].append(user_id)
            self.admins.add(user_id)
            self._save_data()
    
    def remove_admin(self, user_id: str):
        """移除管理员"""
        if user_id in self.data["admins"]:
            self.data["admins"].remove(user_id)
            self.admins.discard(user_id)
            self._save_data()
    
    def is_admin(self, user_id: str, is_group_owner: bool = False) -> bool:
        """
        检查是否为管理员

        Args:
            user_id: 用户ID
            is_group_owner: 是否为群主

        Returns:
            是否为管理员
        """
        # 群主自动拥有管理员权限
        if is_group_owner:
            return True

        # 检查配置的管理员列表
        return user_id in self.admins or user_id in self.data["admins"]
    
    def record_message(self, group_id: str, user_id: str, content: str, timestamp: float = None):
        """记录群聊消息"""
        if timestamp is None:
            timestamp = time.time()
        
        # 生成消息唯一标识（基于内容和用户）
        message_hash = hashlib.md5(f"{user_id}:{content}".encode()).hexdigest()
        
        if group_id not in self.data["messages"]:
            self.data["messages"][group_id] = []
        
        # 检查是否为重复消息
        for msg in self.data["messages"][group_id]:
            if msg["hash"] == message_hash:
                return False  # 重复消息，不记录
        
        # 记录新消息
        message_data = {
            "user_id": user_id,
            "content": content,
            "timestamp": timestamp,
            "hash": message_hash,
            "datetime": datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.data["messages"][group_id].append(message_data)
        
        # 保持最近1000条消息（避免数据过大）
        if len(self.data["messages"][group_id]) > 1000:
            self.data["messages"][group_id] = self.data["messages"][group_id][-1000:]
        
        self._save_data()
        return True
    
    def create_lottery(self, group_id: str, admin_user_id: str, days: int, draw_time: str, is_group_owner: bool = False) -> Tuple[bool, str]:
        """
        创建抽奖活动

        Args:
            group_id: 群组ID
            admin_user_id: 管理员用户ID
            days: 选取多少天内的消息
            draw_time: 开奖时间（格式：YYYY-MM-DD HH:MM 或 相对时间如 "1小时后"）
            is_group_owner: 是否为群主

        Returns:
            (成功标志, 消息)
        """
        if not self.is_admin(admin_user_id, is_group_owner):
            return False, "❌ 只有管理员或群主才能创建抽奖活动"
        
        if group_id in self.active_lotteries:
            return False, "❌ 该群已有进行中的抽奖活动"
        
        # 解析开奖时间
        try:
            if "小时后" in draw_time:
                hours = int(draw_time.replace("小时后", ""))
                draw_timestamp = time.time() + hours * 3600
            elif "分钟后" in draw_time:
                minutes = int(draw_time.replace("分钟后", ""))
                draw_timestamp = time.time() + minutes * 60
            else:
                # 解析具体时间
                draw_datetime = datetime.strptime(draw_time, "%Y-%m-%d %H:%M")
                draw_timestamp = draw_datetime.timestamp()
        except Exception as e:
            return False, f"❌ 开奖时间格式错误: {e}"
        
        if draw_timestamp <= time.time():
            return False, "❌ 开奖时间必须在未来"
        
        # 创建抽奖活动
        lottery_id = f"{group_id}_{int(time.time())}"
        lottery_info = {
            "lottery_id": lottery_id,
            "group_id": group_id,
            "admin_user_id": admin_user_id,
            "days": days,
            "draw_timestamp": draw_timestamp,
            "draw_time": datetime.fromtimestamp(draw_timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": "active"
        }
        
        self.active_lotteries[group_id] = lottery_info
        self._save_data()
        
        return True, f"✅ 抽奖活动创建成功！\n" \
                     f"📅 选取范围: 最近{days}天的消息\n" \
                     f"⏰ 开奖时间: {lottery_info['draw_time']}\n" \
                     f"🎫 抽奖ID: {lottery_id}"
    
    def cancel_lottery(self, group_id: str, admin_user_id: str, is_group_owner: bool = False) -> Tuple[bool, str]:
        """取消抽奖活动"""
        if not self.is_admin(admin_user_id, is_group_owner):
            return False, "❌ 只有管理员或群主才能取消抽奖活动"
        
        if group_id not in self.active_lotteries:
            return False, "❌ 该群没有进行中的抽奖活动"
        
        lottery_info = self.active_lotteries[group_id]
        del self.active_lotteries[group_id]
        self._save_data()
        
        return True, f"✅ 抽奖活动已取消\n🎫 抽奖ID: {lottery_info['lottery_id']}"
    
    def draw_lottery(self, group_id: str, admin_user_id: str = None, is_group_owner: bool = False) -> Tuple[bool, str]:
        """
        执行抽奖

        Args:
            group_id: 群组ID
            admin_user_id: 管理员用户ID（可选，用于手动开奖）
            is_group_owner: 是否为群主

        Returns:
            (成功标志, 结果消息)
        """
        if group_id not in self.active_lotteries:
            return False, "❌ 该群没有进行中的抽奖活动"

        lottery_info = self.active_lotteries[group_id]

        # 检查是否到开奖时间（手动开奖除外）
        if admin_user_id:
            if not self.is_admin(admin_user_id, is_group_owner):
                return False, "❌ 只有管理员或群主才能手动开奖"
        else:
            if time.time() < lottery_info["draw_timestamp"]:
                return False, "❌ 还未到开奖时间"
        
        # 获取指定时间范围内的消息
        days = lottery_info["days"]
        cutoff_time = time.time() - days * 24 * 3600
        
        eligible_messages = []
        if group_id in self.data["messages"]:
            for msg in self.data["messages"][group_id]:
                if msg["timestamp"] >= cutoff_time:
                    eligible_messages.append(msg)
        
        if not eligible_messages:
            # 移除活动
            del self.active_lotteries[group_id]
            self._save_data()
            return False, f"❌ 最近{days}天内没有符合条件的消息"
        
        # 随机选择获奖消息
        winner_message = random.choice(eligible_messages)
        
        # 保存抽奖结果
        lottery_result = {
            **lottery_info,
            "winner_user_id": winner_message["user_id"],
            "winner_message": winner_message["content"],
            "winner_time": winner_message["datetime"],
            "total_messages": len(eligible_messages),
            "draw_actual_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": "completed"
        }
        
        self.data["lotteries"][lottery_info["lottery_id"]] = lottery_result
        del self.active_lotteries[group_id]
        self._save_data()
        
        # 检查是否启用@everyone提醒
        try:
            import config
            notify_all = getattr(config, 'LOTTERY_NOTIFY_ALL', True)
            at_everyone = "@everyone " if notify_all else ""
        except:
            at_everyone = ""

        result_text = f"""
🎉 抽奖结果公布！{at_everyone}

🏆 获奖用户: {winner_message["user_id"]}
💬 获奖消息: {winner_message["content"]}
📅 发言时间: {winner_message["datetime"]}
📊 参与消息数: {len(eligible_messages)}条
🎫 抽奖ID: {lottery_info["lottery_id"]}

🎊 恭喜获奖者！感谢大家的参与！
        """.strip()
        
        return True, result_text
    
    def get_lottery_status(self, group_id: str) -> str:
        """获取抽奖状态"""
        if group_id not in self.active_lotteries:
            return "❌ 该群没有进行中的抽奖活动"
        
        lottery_info = self.active_lotteries[group_id]
        remaining_time = lottery_info["draw_timestamp"] - time.time()
        
        if remaining_time > 0:
            hours = int(remaining_time // 3600)
            minutes = int((remaining_time % 3600) // 60)
            time_str = f"{hours}小时{minutes}分钟"
        else:
            time_str = "已到开奖时间"
        
        # 统计符合条件的消息数
        days = lottery_info["days"]
        cutoff_time = time.time() - days * 24 * 3600
        eligible_count = 0
        
        if group_id in self.data["messages"]:
            for msg in self.data["messages"][group_id]:
                if msg["timestamp"] >= cutoff_time:
                    eligible_count += 1
        
        return f"""
🎲 抽奖活动进行中

📅 选取范围: 最近{lottery_info["days"]}天
⏰ 开奖时间: {lottery_info["draw_time"]}
⏳ 剩余时间: {time_str}
📊 符合条件消息: {eligible_count}条
🎫 抽奖ID: {lottery_info["lottery_id"]}
        """.strip()
    
    def get_admin_list(self) -> List[str]:
        """获取管理员列表"""
        return self.data["admins"].copy()
    
    def get_lottery_history(self, group_id: str, limit: int = 5) -> str:
        """获取抽奖历史"""
        history = []
        for lottery_id, result in self.data["lotteries"].items():
            if result["group_id"] == group_id:
                history.append(result)
        
        if not history:
            return "📝 该群暂无抽奖历史"
        
        # 按时间排序，最新的在前
        history.sort(key=lambda x: x["draw_actual_time"], reverse=True)
        history = history[:limit]
        
        result_text = f"📚 最近{len(history)}次抽奖历史:\n\n"
        
        for i, result in enumerate(history, 1):
            result_text += f"{i}. 🎫 {result['lottery_id']}\n"
            result_text += f"   🏆 获奖者: {result['winner_user_id']}\n"
            result_text += f"   💬 获奖消息: {result['winner_message'][:30]}...\n"
            result_text += f"   📅 开奖时间: {result['draw_actual_time']}\n\n"
        
        return result_text.strip()


# 创建全局抽奖系统实例
lottery_system = LotterySystem()
