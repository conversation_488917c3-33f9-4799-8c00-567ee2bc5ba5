#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试群主权限功能
"""

import os
from lottery_system import LotterySystem

def test_group_owner_permissions():
    """测试群主权限功能"""
    print("🧪 测试群主权限功能")
    print("=" * 50)
    
    # 创建测试用的抽奖系统实例
    test_lottery = LotterySystem("test_group_owner.json")
    
    # 测试数据
    test_group_id = "test_group_123"
    admin_user_id = "admin_123"
    group_owner_id = "owner_456"
    normal_user_id = "user_789"
    
    print("1. 测试基础管理员功能")
    print("-" * 30)
    
    # 添加配置管理员
    test_lottery.add_admin(admin_user_id)
    print(f"✅ 添加配置管理员: {admin_user_id}")
    
    # 测试权限检查
    print(f"✅ 配置管理员权限: {test_lottery.is_admin(admin_user_id)} (应为True)")
    print(f"✅ 群主权限: {test_lottery.is_admin(group_owner_id, is_group_owner=True)} (应为True)")
    print(f"✅ 普通用户权限: {test_lottery.is_admin(normal_user_id)} (应为False)")
    
    print("\n2. 测试群主创建抽奖")
    print("-" * 30)
    
    # 群主创建抽奖（无需配置）
    success, msg = test_lottery.create_lottery(test_group_id, group_owner_id, 7, "1小时后", is_group_owner=True)
    print(f"✅ 群主创建抽奖: {success}")
    if success:
        print(f"   消息: {msg[:100]}...")
    
    print("\n3. 测试普通用户尝试管理抽奖")
    print("-" * 30)
    
    # 普通用户尝试取消抽奖
    success, msg = test_lottery.cancel_lottery(test_group_id, normal_user_id, is_group_owner=False)
    print(f"❌ 普通用户取消抽奖: {success} - {msg}")
    
    # 群主取消抽奖
    success, msg = test_lottery.cancel_lottery(test_group_id, group_owner_id, is_group_owner=True)
    print(f"✅ 群主取消抽奖: {success} - {msg}")
    
    print("\n4. 测试配置管理员和群主权限对比")
    print("-" * 30)
    
    # 配置管理员创建抽奖
    success1, msg1 = test_lottery.create_lottery("group_1", admin_user_id, 3, "30分钟后", is_group_owner=False)
    print(f"✅ 配置管理员创建抽奖: {success1}")
    
    # 群主创建抽奖（不同群）
    success2, msg2 = test_lottery.create_lottery("group_2", group_owner_id, 5, "2小时后", is_group_owner=True)
    print(f"✅ 群主创建抽奖: {success2}")
    
    print("\n5. 测试权限检查边界情况")
    print("-" * 30)
    
    # 测试空用户ID
    print(f"❌ 空用户ID权限: {test_lottery.is_admin('', is_group_owner=False)} (应为False)")
    print(f"✅ 空用户ID但是群主: {test_lottery.is_admin('', is_group_owner=True)} (应为True)")
    
    # 测试None用户ID
    print(f"❌ None用户ID权限: {test_lottery.is_admin(None, is_group_owner=False)} (应为False)")
    print(f"✅ None用户ID但是群主: {test_lottery.is_admin(None, is_group_owner=True)} (应为True)")
    
    print("\n🎉 群主权限测试完成！")
    
    # 清理测试文件
    try:
        os.remove("test_group_owner.json")
        print("🧹 清理测试文件完成")
    except:
        pass

def test_permission_scenarios():
    """测试各种权限场景"""
    print("\n🎭 测试权限场景")
    print("=" * 30)
    
    test_lottery = LotterySystem("test_scenarios.json")
    
    scenarios = [
        ("普通用户", "user_123", False, False),
        ("配置管理员", "admin_123", False, True),
        ("群主", "owner_123", True, True),
        ("群主+配置管理员", "super_admin", True, True),
    ]
    
    # 添加配置管理员
    test_lottery.add_admin("admin_123")
    test_lottery.add_admin("super_admin")
    
    for name, user_id, is_owner, expected in scenarios:
        result = test_lottery.is_admin(user_id, is_owner)
        status = "✅" if result == expected else "❌"
        print(f"{status} {name}: {result} (期望: {expected})")
    
    # 清理测试文件
    try:
        os.remove("test_scenarios.json")
    except:
        pass

if __name__ == "__main__":
    test_group_owner_permissions()
    test_permission_scenarios()
