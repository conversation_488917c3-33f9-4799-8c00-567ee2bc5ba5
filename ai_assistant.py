#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI助手模块
用于处理OpenAI大模型的调用和回复
"""

import asyncio
import aiohttp
import json
import time
import re
from typing import Optional, Dict, Any
import config

class AIAssistant:
    """AI助手类，负责与OpenAI API交互"""
    
    def __init__(self):
        self.api_key = config.OPENAI_API_KEY
        self.api_base = config.OPENAI_API_BASE
        self.model = config.OPENAI_MODEL
        self.max_tokens = config.AI_MAX_TOKENS
        self.temperature = config.AI_TEMPERATURE
        self.system_prompt = config.AI_SYSTEM_PROMPT
        self.enabled = config.AI_ENABLED
        
        # 简单的对话历史缓存（可以扩展为更复杂的实现）
        self.conversation_cache = {}
        self.cache_expire_time = 300  # 5分钟过期
        
    def is_enabled(self) -> bool:
        """检查AI功能是否启用"""
        return self.enabled and self.api_key and self.api_key != "your-openai-api-key-here"

    def _filter_content(self, content: str) -> str:
        """过滤AI回复内容，移除不被允许的URL和敏感内容"""
        if not content:
            return content

        # 移除常见的不被允许的URL模式
        url_patterns = [
            r'https?://[^\s]*t\.circle[^\s]*',  # t.circle相关URL
            r'https?://[^\s]*\.circle[^\s]*',   # 其他.circle域名
            r'https?://[^\s]*circle[^\s]*',     # 包含circle的URL
            r'https?://[^\s]*\.co[^\s]*',       # .co域名（可能被误判）
            r'https?://[^\s]*\.tk[^\s]*',       # .tk域名
            r'https?://[^\s]*\.ml[^\s]*',       # .ml域名
            r'https?://[^\s]+',                 # 所有HTTP/HTTPS链接
        ]

        filtered_content = content
        for pattern in url_patterns:
            filtered_content = re.sub(pattern, '[链接已过滤]', filtered_content, flags=re.IGNORECASE)

        # 移除可能的敏感词汇
        sensitive_patterns = [
            r't\.circle',
            r'circle\.com',
            r'短链接',
            r'点击链接',
            r'访问.*链接',
            r'打开.*网址',
        ]

        for pattern in sensitive_patterns:
            filtered_content = re.sub(pattern, '[内容已过滤]', filtered_content, flags=re.IGNORECASE)

        # 清理多余的空格和换行
        filtered_content = re.sub(r'\s+', ' ', filtered_content).strip()

        return filtered_content

    def _handle_drawing_request(self, user_message: str) -> str:
        """处理绘画请求，返回文字描述而不是图片链接"""
        drawing_keywords = ['画', '绘制', '绘画', '画一个', '画只', '画个', 'draw', 'paint']

        if any(keyword in user_message.lower() for keyword in drawing_keywords):
            # 提取绘画主题
            subject = "图画"
            for keyword in drawing_keywords:
                if keyword in user_message:
                    parts = user_message.split(keyword)
                    if len(parts) > 1:
                        subject = parts[1].strip()
                        break

            return f"我理解你想要画{subject}！虽然我无法直接生成图片，但我可以为你描述如何画{subject}：\n\n" \
                   f"1. 先画出{subject}的基本轮廓\n" \
                   f"2. 添加主要特征和细节\n" \
                   f"3. 用适当的颜色进行填充\n" \
                   f"4. 最后添加阴影和高光效果\n\n" \
                   f"你可以根据这个步骤来创作你的{subject}作品！"

        return None
    
    def _clean_expired_cache(self):
        """清理过期的对话缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, data in self.conversation_cache.items()
            if current_time - data['timestamp'] > self.cache_expire_time
        ]
        for key in expired_keys:
            del self.conversation_cache[key]
    
    def _get_conversation_history(self, user_id: str) -> list:
        """获取用户的对话历史"""
        self._clean_expired_cache()
        
        if user_id in self.conversation_cache:
            return self.conversation_cache[user_id]['messages']
        return []
    
    def _update_conversation_history(self, user_id: str, user_message: str, ai_response: str):
        """更新用户的对话历史"""
        if user_id not in self.conversation_cache:
            self.conversation_cache[user_id] = {
                'messages': [],
                'timestamp': time.time()
            }
        
        # 添加用户消息和AI回复
        self.conversation_cache[user_id]['messages'].extend([
            {"role": "user", "content": user_message},
            {"role": "assistant", "content": ai_response}
        ])
        
        # 限制历史消息数量（保留最近的10条对话）
        if len(self.conversation_cache[user_id]['messages']) > 20:
            self.conversation_cache[user_id]['messages'] = self.conversation_cache[user_id]['messages'][-20:]
        
        # 更新时间戳
        self.conversation_cache[user_id]['timestamp'] = time.time()
    
    async def get_ai_response(self, user_message: str, user_id: str = "default") -> Optional[str]:
        """
        获取AI回复

        Args:
            user_message: 用户消息
            user_id: 用户ID，用于维护对话历史

        Returns:
            AI回复内容，如果失败返回None
        """
        if not self.is_enabled():
            return None

        # 检查是否是绘画请求，如果是则直接返回文字描述
        drawing_response = self._handle_drawing_request(user_message)
        if drawing_response:
            return drawing_response
            
        try:
            # 构建消息历史
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # 添加历史对话
            history = self._get_conversation_history(user_id)
            messages.extend(history)
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": user_message})
            
            # 准备API请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": messages,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature,
                "stream": False
            }
            
            # 发送API请求
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    f"{self.api_base}/chat/completions",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        ai_response = result['choices'][0]['message']['content'].strip()

                        # 过滤AI回复内容
                        filtered_response = self._filter_content(ai_response)

                        # 更新对话历史（使用过滤后的内容）
                        self._update_conversation_history(user_id, user_message, filtered_response)

                        return filtered_response
                    else:
                        error_text = await response.text()
                        print(f"❌ OpenAI API错误 {response.status}: {error_text}")
                        return None
                        
        except asyncio.TimeoutError:
            print("❌ OpenAI API请求超时")
            return None
        except Exception as e:
            print(f"❌ AI回复生成失败: {e}")
            return None
    
    def clear_conversation_history(self, user_id: str = None):
        """清除对话历史"""
        if user_id:
            if user_id in self.conversation_cache:
                del self.conversation_cache[user_id]
        else:
            self.conversation_cache.clear()
    
    def get_status(self) -> Dict[str, Any]:
        """获取AI助手状态信息"""
        return {
            "enabled": self.is_enabled(),
            "model": self.model,
            "api_base": self.api_base,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "cached_conversations": len(self.conversation_cache)
        }


# 创建全局AI助手实例
ai_assistant = AIAssistant()


async def test_ai_assistant():
    """测试AI助手功能"""
    print("🧪 测试AI助手...")
    
    if not ai_assistant.is_enabled():
        print("❌ AI助手未启用，请检查配置")
        return
    
    # 测试简单对话
    test_message = "你好，请介绍一下自己"
    response = await ai_assistant.get_ai_response(test_message, "test_user")
    
    if response:
        print(f"✅ AI回复测试成功:")
        print(f"用户: {test_message}")
        print(f"AI: {response}")
    else:
        print("❌ AI回复测试失败")
    
    # 显示状态
    status = ai_assistant.get_status()
    print(f"\n📊 AI助手状态: {status}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_ai_assistant())
