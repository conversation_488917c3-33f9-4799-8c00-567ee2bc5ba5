#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的QQ机器人 - Hello消息回复
专注于被动回复hello消息，避免权限问题
"""

import asyncio
import botpy
from botpy import logging
import config

# 设置日志级别
_log = logging.get_logger()


class SimpleHelloClient(botpy.Client):
    """简单的Hello回复机器人"""
    
    async def on_ready(self):
        """当机器人启动完成时触发"""
        print(f"✅ 简单Hello机器人 {self.robot.name} 已启动!")
        print(f"📱 机器人ID: {self.robot.id}")
        print("🎯 机器人正在等待群聊@消息...")
        print("\n📋 使用说明:")
        print("1. 在群聊中@机器人")
        print("2. 发送任何包含'hello'的消息")
        print("3. 机器人会回复'hello'")
        print("4. 按 Ctrl+C 停止机器人")
        print("-" * 50)
        
    async def on_group_at_message_create(self, message):
        """当机器人在群聊中被@时触发"""
        print(f"📨 收到群聊@消息: {message.content}")
        _log.info(f"收到群聊@消息: {message.content}")
        
        # 检查消息内容
        content = message.content.lower()
        
        if "hello" in content or "你好" in content:
            # 直接回复hello
            try:
                await message.reply(content="hello")
                print("✅ 已回复hello消息")
                _log.info("已回复hello消息")
            except Exception as e:
                print(f"❌ 回复hello消息失败: {e}")
                _log.error(f"回复hello消息失败: {e}")
                
        elif "测试" in content:
            try:
                await message.reply(content="✅ 机器人运行正常！发送包含'hello'的消息来测试hello回复功能")
                print("✅ 回复了测试消息")
            except Exception as e:
                print(f"❌ 回复测试消息失败: {e}")
                
        elif "帮助" in content:
            help_text = """
🤖 简单Hello机器人：
• @我 + 包含"hello"的消息 - 回复hello
• @我 + 包含"你好"的消息 - 回复hello  
• @我 + "测试" - 测试机器人状态
• @我 + "帮助" - 显示此帮助

💡 这是被动回复模式，避免主动消息权限问题
            """
            try:
                await message.reply(content=help_text)
                print("✅ 显示了帮助信息")
            except Exception as e:
                print(f"❌ 显示帮助失败: {e}")
                
        else:
            # 默认回复
            try:
                await message.reply(content="我收到了你的消息！发送包含'hello'的消息来测试hello回复功能，发送'帮助'查看更多命令。")
                print("💬 回复了默认消息")
            except Exception as e:
                print(f"❌ 回复默认消息失败: {e}")

    async def on_c2c_message_create(self, message):
        """当收到私聊消息时触发"""
        print(f"📩 收到私聊消息: {message.content}")
        _log.info(f"收到私聊消息: {message.content}")
        
        if "hello" in message.content.lower():
            try:
                await message.reply(content="hello")
                print("✅ 私聊回复hello消息")
            except Exception as e:
                print(f"❌ 私聊回复失败: {e}")
        else:
            try:
                await message.reply(content="收到私聊消息！发送'hello'来测试hello回复功能。")
                print("✅ 私聊回复默认消息")
            except Exception as e:
                print(f"❌ 私聊回复失败: {e}")


async def main():
    """主函数"""
    print("🚀 简单Hello机器人启动中...")
    print("=" * 50)
    
    # 使用默认的 intents
    intents = botpy.Intents.default()
    
    client = SimpleHelloClient(intents=intents, is_sandbox=config.IS_SANDBOX)
    
    try:
        # 启动机器人
        await client.start(appid=config.BOT_ID, secret=config.BOT_SECRET)
    except Exception as e:
        print(f"❌ 简单Hello机器人启动失败: {e}")
        _log.error(f"简单Hello机器人启动失败: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 简单Hello机器人已停止")
    except Exception as e:
        print(f"❌ 简单Hello机器人运行失败: {e}")
