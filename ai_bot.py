#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI智能QQ机器人
集成OpenAI大模型，能够智能回复用户消息
"""

import asyncio
import botpy
from botpy import logging
import config
from ai_assistant import ai_assistant
import datetime

# 设置日志级别
_log = logging.get_logger()


class AIBotClient(botpy.Client):
    """AI智能QQ机器人客户端类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ai_assistant = ai_assistant
        
        # 预定义指令列表（这些指令不会发送给AI）
        self.predefined_commands = [
            "帮助", "help", "时间", "time", "ping", "状态", "status",
            "清除历史", "clear", "ai状态", "ai_status"
        ]
    
    async def on_ready(self):
        """当机器人启动完成时触发"""
        print(f"✅ AI智能机器人 {self.robot.name} 已启动!")
        print(f"📱 机器人ID: {self.robot.id}")
        
        # 检查AI功能状态
        if self.ai_assistant.is_enabled():
            print("🤖 AI功能已启用")
            status = self.ai_assistant.get_status()
            print(f"   模型: {status['model']}")
            print(f"   API地址: {status['api_base']}")
        else:
            print("⚠️ AI功能未启用，请检查配置文件中的OpenAI设置")
        
        print("🎯 机器人正在等待消息...")
        print("\n📋 功能说明:")
        print("• @机器人 + 任意消息 - AI智能回复")
        print("• @机器人 + 预定义指令 - 执行特定功能")
        print("• 支持上下文对话记忆")
        print("-" * 50)
    
    def _is_predefined_command(self, content: str) -> bool:
        """检查是否为预定义指令"""
        content_lower = content.lower().strip()
        return any(cmd in content_lower for cmd in self.predefined_commands)
    
    async def _handle_predefined_command(self, message, content: str):
        """处理预定义指令"""
        content_lower = content.lower().strip()
        
        if "帮助" in content_lower or "help" in content_lower:
            help_text = """
🤖 AI智能机器人帮助：

📝 基础指令：
• 帮助/help - 显示此帮助信息
• 时间/time - 显示当前时间
• ping - 测试机器人响应
• 状态/status - 显示机器人状态
• ai状态 - 显示AI功能状态
• 清除历史/clear - 清除你的对话历史

🧠 AI功能：
• 直接@我说话 - AI智能回复
• 支持上下文对话
• 支持多轮对话记忆

💡 提示：除了上述指令外，任何消息都会发送给AI进行智能回复
            """
            await message.reply(content=help_text)
            
        elif "时间" in content_lower or "time" in content_lower:
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await message.reply(content=f"⏰ 当前时间: {now}")
            
        elif "ping" in content_lower:
            await message.reply(content="pong! 🏓 机器人运行正常")
            
        elif "状态" in content_lower or "status" in content_lower:
            status_text = f"""
📊 机器人状态：
• 机器人名称: {self.robot.name}
• 机器人ID: {self.robot.id}
• AI功能: {'✅ 已启用' if self.ai_assistant.is_enabled() else '❌ 未启用'}
• 运行状态: ✅ 正常
            """
            await message.reply(content=status_text)
            
        elif "ai状态" in content_lower:
            if self.ai_assistant.is_enabled():
                status = self.ai_assistant.get_status()
                ai_status_text = f"""
🤖 AI功能状态：
• 状态: ✅ 已启用
• 模型: {status['model']}
• 最大tokens: {status['max_tokens']}
• 创造性: {status['temperature']}
• 缓存对话数: {status['cached_conversations']}
                """
            else:
                ai_status_text = "❌ AI功能未启用，请检查配置文件"
            await message.reply(content=ai_status_text)
            
        elif "清除历史" in content_lower or "clear" in content_lower:
            user_id = str(message.author.id)
            self.ai_assistant.clear_conversation_history(user_id)
            await message.reply(content="✅ 已清除你的对话历史")
            
        else:
            await message.reply(content="未知指令，发送'帮助'查看可用指令")
    
    async def _handle_ai_message(self, message, content: str):
        """处理AI消息"""
        if not self.ai_assistant.is_enabled():
            await message.reply(content="❌ AI功能未启用，请联系管理员配置OpenAI设置")
            return
        
        # 显示正在思考的提示
        thinking_msg = await message.reply(content="🤔 正在思考中...")
        
        try:
            # 获取用户ID作为对话标识
            user_id = str(message.author.id)
            
            # 调用AI获取回复
            ai_response = await self.ai_assistant.get_ai_response(content, user_id)
            
            if ai_response:
                # 发送AI回复
                await message.reply(content=f"🤖 {ai_response}")
                print(f"✅ AI回复成功: {content[:30]}... -> {ai_response[:30]}...")
                _log.info(f"AI回复成功: 用户{user_id}")
            else:
                await message.reply(content="❌ AI回复生成失败，请稍后重试")
                print("❌ AI回复生成失败")
                _log.error("AI回复生成失败")
                
        except Exception as e:
            await message.reply(content="❌ 处理消息时出现错误，请稍后重试")
            print(f"❌ 处理AI消息时出错: {e}")
            _log.error(f"处理AI消息时出错: {e}")
    
    async def on_group_at_message_create(self, message):
        """当机器人在群聊中被@时触发"""
        content = message.content.strip()
        print(f"📨 收到群聊@消息: {content}")
        _log.info(f"收到群聊@消息: {content}")
        
        # 检查是否为预定义指令
        if self._is_predefined_command(content):
            await self._handle_predefined_command(message, content)
        else:
            # 发送给AI处理
            await self._handle_ai_message(message, content)
    
    async def on_c2c_message_create(self, message):
        """当收到私聊消息时触发"""
        content = message.content.strip()
        print(f"📩 收到私聊消息: {content}")
        _log.info(f"收到私聊消息: {content}")
        
        # 私聊消息也支持AI回复
        if self._is_predefined_command(content):
            await self._handle_predefined_command(message, content)
        else:
            await self._handle_ai_message(message, content)


async def main():
    """主函数"""
    print("🚀 AI智能机器人启动中...")
    print("=" * 50)
    
    # 使用默认的 intents
    intents = botpy.Intents.default()
    
    client = AIBotClient(intents=intents, is_sandbox=config.IS_SANDBOX)
    
    try:
        # 启动机器人
        await client.start(appid=config.BOT_ID, secret=config.BOT_SECRET)
    except Exception as e:
        print(f"❌ AI智能机器人启动失败: {e}")
        _log.error(f"AI智能机器人启动失败: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 AI智能机器人已停止")
    except Exception as e:
        print(f"❌ AI智能机器人运行失败: {e}")
