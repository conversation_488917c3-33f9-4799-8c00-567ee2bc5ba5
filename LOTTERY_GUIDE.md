# 🎲 QQ机器人抽奖系统使用指南

## 📋 功能概述

抽奖系统是一个完整的群聊抽奖解决方案，具有以下特性：

- 🎯 **智能消息收集** - 自动记录群聊消息，排除重复内容
- 👑 **管理员权限控制** - 只有管理员可以创建和管理抽奖
- ⏰ **灵活时间设置** - 支持相对时间和绝对时间设置
- 📊 **详细统计信息** - 显示参与消息数量和抽奖历史
- 🔒 **数据持久化** - 抽奖数据自动保存到文件

## 🚀 快速开始

### 1. 配置管理员

在 `config.py` 文件中添加抽奖管理员：

```python
# 抽奖系统配置
LOTTERY_ENABLED = True  # 启用抽奖功能
LOTTERY_ADMINS = [
    "your_user_id_here",  # 替换为实际的用户ID
    # 可以添加多个管理员
]
```

### 2. 启动机器人

```bash
C:/Python311/python.exe ai_lottery_bot.py
```

### 3. 测试功能

在群聊中发送：
```
@机器人 抽奖帮助
```

## 👑 管理员指令

### 创建抽奖活动

**格式**：`创建抽奖 [天数] [开奖时间]`

**示例**：
```
@机器人 创建抽奖 7 2小时后
@机器人 创建抽奖 3 30分钟后
@机器人 创建抽奖 1 2024-01-01 20:00
```

**参数说明**：
- `天数`：选取最近多少天内的消息参与抽奖
- `开奖时间`：可以是相对时间（如"2小时后"）或绝对时间（如"2024-01-01 20:00"）

### 管理抽奖活动

```bash
# 取消当前抽奖
@机器人 取消抽奖

# 手动立即开奖
@机器人 手动开奖

# 查看抽奖状态
@机器人 抽奖状态
```

### 管理员管理

```bash
# 查看管理员列表
@机器人 管理员列表

# 添加管理员（需要在配置文件中手动添加）
@机器人 添加管理员
```

## 👥 用户指令

所有用户都可以使用的指令：

```bash
# 查看抽奖帮助
@机器人 抽奖帮助

# 查看当前抽奖状态
@机器人 抽奖状态

# 查看抽奖历史
@机器人 抽奖历史

# 查看管理员列表
@机器人 管理员列表
```

## 🎯 抽奖规则

### 消息收集规则
1. **自动收集**：机器人自动记录群聊中的所有消息
2. **去重处理**：相同用户发送的相同内容只记录一次
3. **排除规则**：排除@机器人的消息和空消息
4. **时间范围**：只有在指定天数内的消息才参与抽奖

### 抽奖算法
1. **随机选择**：从符合条件的消息中随机选择一条
2. **公平公正**：每条符合条件的消息都有相等的中奖概率
3. **结果公示**：显示获奖消息、用户、时间等详细信息

## 📊 使用示例

### 完整抽奖流程

1. **管理员创建抽奖**：
   ```
   @机器人 创建抽奖 7 1小时后
   ```
   
   机器人回复：
   ```
   ✅ 抽奖活动创建成功！
   📅 选取范围: 最近7天的消息
   ⏰ 开奖时间: 2024-01-01 21:00:00
   🎫 抽奖ID: group123_1704117600
   ```

2. **用户查看状态**：
   ```
   @机器人 抽奖状态
   ```
   
   机器人回复：
   ```
   🎲 抽奖活动进行中
   
   📅 选取范围: 最近7天
   ⏰ 开奖时间: 2024-01-01 21:00:00
   ⏳ 剩余时间: 0小时45分钟
   📊 符合条件消息: 156条
   🎫 抽奖ID: group123_1704117600
   ```

3. **自动开奖**（到时间后）：
   ```
   🎉 抽奖结果公布！
   
   🏆 获奖用户: user123
   💬 获奖消息: 今天天气真不错！
   📅 发言时间: 2024-01-01 15:30:00
   📊 参与消息数: 156条
   🎫 抽奖ID: group123_1704117600
   
   恭喜获奖者！🎊
   ```

## 🔧 高级功能

### 数据持久化
- 所有抽奖数据保存在 `lottery_data.json` 文件中
- 包括消息记录、抽奖历史、管理员列表
- 机器人重启后数据不会丢失

### 消息管理
- 自动保持最近1000条消息（避免数据过大）
- 消息去重基于用户ID和消息内容的MD5哈希
- 支持查看详细的抽奖历史记录

### 权限控制
- 严格的管理员权限验证
- 只有管理员可以创建、取消、手动开奖
- 普通用户只能查看状态和历史

## ⚠️ 注意事项

### 配置要求
1. **管理员配置**：必须在配置文件中正确设置管理员用户ID
2. **权限验证**：确保管理员用户ID准确无误
3. **功能开关**：确保 `LOTTERY_ENABLED = True`

### 使用限制
1. **单群限制**：每个群同时只能有一个进行中的抽奖活动
2. **时间限制**：开奖时间必须在未来
3. **消息要求**：需要有符合条件的消息才能开奖

### 数据安全
1. **定期备份**：建议定期备份 `lottery_data.json` 文件
2. **权限保护**：妥善保管管理员账号
3. **日志监控**：关注机器人运行日志

## 🎊 成功标志

当看到以下输出时，表示抽奖系统配置成功：

```
✅ AI智能机器人+抽奖系统 早鸟-测试中 已启动!
📱 机器人ID: 1411261171501187073
🤖 AI功能已启用
   模型: doubao-1-5-vision-pro-32k-250115
🎲 抽奖功能已启用
   管理员数量: 1
🎯 机器人正在等待消息...
```

现在你的QQ机器人已经具备了完整的抽奖功能！🎲✨
