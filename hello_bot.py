#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QQ机器人 - 发送Hello消息测试
专门用于测试在群里发送hello消息的功能
"""

import asyncio
import botpy
from botpy import logging
import config

# 设置日志级别
_log = logging.get_logger()


class HelloClient(botpy.Client):
    """专门用于发送Hello消息的QQ机器人客户端类"""
    
    async def on_ready(self):
        """当机器人启动完成时触发"""
        print(f"✅ Hello机器人 {self.robot.name} 已启动!")
        print(f"📱 机器人ID: {self.robot.id}")
        print("🎯 机器人正在等待群聊消息...")
        print("\n📋 使用说明:")
        print("1. 将机器人添加到测试群聊")
        print("2. 在群聊中@机器人并发送 '发送hello' 命令")
        print("3. 机器人会在群里发送hello消息")
        print("4. 按 Ctrl+C 停止机器人")
        print("-" * 50)
        
    async def on_group_add_robot(self, message):
        """当机器人被添加到群聊时触发 - 自动发送hello"""
        _log.info(f"机器人被添加到群聊: {message.group_openid}")
        print(f"🎉 机器人被添加到新群聊!")

        # 尝试发送欢迎hello消息（被动消息）
        try:
            await self.api.post_group_message(
                group_openid=message.group_openid,
                msg_type=0,  # 文本消息
                content="hello! 我是QQ机器人，很高兴加入这个群聊！",
                event_id=getattr(message, 'event_id', None)  # 使用事件ID
            )
            print("✅ 已自动发送hello欢迎消息到群聊")
            _log.info("自动发送hello欢迎消息成功")
        except Exception as e:
            print(f"⚠️ 自动发送hello消息失败（可能是权限限制）: {e}")
            _log.warning(f"自动发送hello消息失败: {e}")
            print("💡 提示：可以在群聊中@机器人发送'hello'命令来测试回复功能")
        
    async def on_group_at_message_create(self, message):
        """当机器人在群聊中被@时触发"""
        print(f"📨 收到群聊@消息: {message.content}")
        _log.info(f"收到群聊@消息: {message.content}")
        
        # 处理发送hello命令
        if "发送hello" in message.content or "hello" in message.content.lower():
            try:
                # 方法1：直接回复hello消息
                await message.reply(content="hello")
                print("✅ 已回复hello消息")
                _log.info("已回复hello消息")

                # 方法2：尝试发送被动消息到群聊（带event_id和msg_id）
                try:
                    await self.api.post_group_message(
                        group_openid=message.group_openid,
                        msg_type=0,  # 文本消息
                        content="hello (群聊消息)",
                        msg_id=message.id,  # 引用原消息ID
                        msg_seq=1  # 消息序号
                    )
                    print("✅ 已发送hello消息到群聊")
                    _log.info("已发送hello消息到群聊")
                except Exception as e2:
                    print(f"⚠️ 群聊消息发送失败（这是正常的，因为权限限制）: {e2}")
                    _log.warning(f"群聊消息发送失败: {e2}")

            except Exception as e:
                await message.reply(content=f"❌ 回复hello消息失败: {e}")
                print(f"❌ 回复hello消息失败: {e}")
                _log.error(f"回复hello消息失败: {e}")
                
        elif "测试" in message.content:
            await message.reply(content="✅ 机器人运行正常！可以发送 '发送hello' 来测试hello消息功能")
            print("✅ 回复了测试消息")
            
        elif "帮助" in message.content:
            help_text = """
🤖 Hello机器人帮助：
• @我 + "发送hello" - 在群里发送hello消息
• @我 + "hello" - 在群里发送hello消息  
• @我 + "测试" - 测试机器人状态
• @我 + "帮助" - 显示此帮助信息

💡 提示：机器人被添加到群聊时会自动发送hello欢迎消息
            """
            await message.reply(content=help_text)
            print("✅ 显示了帮助信息")
            
        else:
            await message.reply(content="我是Hello机器人！发送 '发送hello' 或 'hello' 来测试功能，发送 '帮助' 查看更多命令。")
            print("💬 回复了默认消息")


async def main():
    """主函数"""
    print("🚀 Hello机器人启动中...")
    print("=" * 50)
    
    # 使用默认的 intents
    intents = botpy.Intents.default()
    
    client = HelloClient(intents=intents, is_sandbox=config.IS_SANDBOX)
    
    try:
        # 启动机器人
        await client.start(appid=config.BOT_ID, secret=config.BOT_SECRET)
    except Exception as e:
        print(f"❌ Hello机器人启动失败: {e}")
        _log.error(f"Hello机器人启动失败: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Hello机器人已停止")
    except Exception as e:
        print(f"❌ Hello机器人运行失败: {e}")
