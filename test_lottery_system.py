#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抽奖系统测试脚本
"""

import time
import os
from lottery_system import LotterySystem

def test_lottery_system():
    """测试抽奖系统功能"""
    print("🧪 测试抽奖系统功能")
    print("=" * 50)
    
    # 创建测试用的抽奖系统实例
    test_lottery = LotterySystem("test_lottery_data.json")
    
    # 测试数据
    test_group_id = "test_group_123"
    admin_user_id = "admin_123"
    normal_user_id = "user_456"
    
    print("1. 测试管理员功能")
    print("-" * 30)
    
    # 添加管理员
    test_lottery.add_admin(admin_user_id)
    print(f"✅ 添加管理员: {admin_user_id}")
    
    # 检查管理员权限
    is_admin = test_lottery.is_admin(admin_user_id)
    is_not_admin = test_lottery.is_admin(normal_user_id)
    print(f"✅ 管理员权限检查: {is_admin} (应为True)")
    print(f"✅ 普通用户权限检查: {is_not_admin} (应为False)")
    
    print("\n2. 测试消息记录功能")
    print("-" * 30)
    
    # 记录测试消息
    messages = [
        ("user_1", "今天天气真不错！"),
        ("user_2", "大家好，我是新人"),
        ("user_3", "有人一起打游戏吗？"),
        ("user_1", "今天天气真不错！"),  # 重复消息
        ("user_4", "晚上吃什么好呢？"),
        ("user_5", "推荐一个好电影"),
    ]
    
    recorded_count = 0
    for user_id, content in messages:
        if test_lottery.record_message(test_group_id, user_id, content):
            recorded_count += 1
            print(f"✅ 记录消息: {user_id} - {content}")
        else:
            print(f"⚠️ 重复消息跳过: {user_id} - {content}")
    
    print(f"📊 总共记录了 {recorded_count} 条消息（去重后）")
    
    print("\n3. 测试创建抽奖功能")
    print("-" * 30)
    
    # 测试非管理员创建抽奖
    success, msg = test_lottery.create_lottery(test_group_id, normal_user_id, 7, "1小时后")
    print(f"❌ 非管理员创建抽奖: {success} - {msg}")
    
    # 测试管理员创建抽奖
    success, msg = test_lottery.create_lottery(test_group_id, admin_user_id, 7, "1小时后")
    print(f"✅ 管理员创建抽奖: {success}")
    print(f"   消息: {msg}")
    
    # 测试重复创建抽奖
    success, msg = test_lottery.create_lottery(test_group_id, admin_user_id, 3, "30分钟后")
    print(f"❌ 重复创建抽奖: {success} - {msg}")
    
    print("\n4. 测试抽奖状态查询")
    print("-" * 30)
    
    status = test_lottery.get_lottery_status(test_group_id)
    print(f"📊 抽奖状态:\n{status}")
    
    print("\n5. 测试手动开奖功能")
    print("-" * 30)
    
    # 测试非管理员手动开奖
    success, msg = test_lottery.draw_lottery(test_group_id, normal_user_id)
    print(f"❌ 非管理员手动开奖: {success} - {msg}")
    
    # 测试管理员手动开奖
    success, msg = test_lottery.draw_lottery(test_group_id, admin_user_id)
    print(f"✅ 管理员手动开奖: {success}")
    if success:
        print(f"🎉 开奖结果:\n{msg}")
    
    print("\n6. 测试抽奖历史查询")
    print("-" * 30)
    
    history = test_lottery.get_lottery_history(test_group_id)
    print(f"📚 抽奖历史:\n{history}")
    
    print("\n7. 测试管理员列表")
    print("-" * 30)
    
    admin_list = test_lottery.get_admin_list()
    print(f"👑 管理员列表: {admin_list}")
    
    print("\n8. 测试取消抽奖功能")
    print("-" * 30)
    
    # 先创建一个新的抽奖
    test_group_id_2 = "test_group_456"
    success, msg = test_lottery.create_lottery(test_group_id_2, admin_user_id, 1, "2小时后")
    print(f"✅ 创建新抽奖: {success}")
    
    # 测试取消抽奖
    success, msg = test_lottery.cancel_lottery(test_group_id_2, admin_user_id)
    print(f"✅ 取消抽奖: {success} - {msg}")
    
    print("\n🎉 抽奖系统测试完成！")
    
    # 清理测试文件
    try:
        os.remove("test_lottery_data.json")
        print("🧹 清理测试文件完成")
    except:
        pass

def test_time_parsing():
    """测试时间解析功能"""
    print("\n🕐 测试时间解析功能")
    print("=" * 30)
    
    test_lottery = LotterySystem("test_time_parsing.json")
    test_lottery.add_admin("admin_test")
    
    test_cases = [
        ("1小时后", True),
        ("30分钟后", True),
        ("2024-12-31 23:59", False),  # 过去时间
        ("2025-12-31 23:59", True),   # 未来时间
        ("invalid_time", False),      # 无效格式
    ]
    
    for time_str, should_succeed in test_cases:
        success, msg = test_lottery.create_lottery("test_group", "admin_test", 1, time_str)
        result = "✅" if success == should_succeed else "❌"
        print(f"{result} 时间格式 '{time_str}': {success} - {msg[:50]}...")
        
        # 如果创建成功，取消抽奖以便下次测试
        if success:
            test_lottery.cancel_lottery("test_group", "admin_test")
    
    # 清理测试文件
    try:
        os.remove("test_time_parsing.json")
    except:
        pass

if __name__ == "__main__":
    test_lottery_system()
    test_time_parsing()
