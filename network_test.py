#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网络连接测试脚本
用于诊断QQ机器人API连接问题
"""

import asyncio
import aiohttp
import socket
import ssl
import config

async def test_dns_resolution():
    """测试DNS解析"""
    print("🔍 测试DNS解析...")
    
    hosts = [
        "sandbox.api.sgroup.qq.com",
        "api.sgroup.qq.com", 
        "bots.qq.com",
        "bot.q.qq.com"
    ]
    
    for host in hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"✅ {host} -> {ip}")
        except socket.gaierror as e:
            print(f"❌ {host} -> DNS解析失败: {e}")

async def test_http_connection():
    """测试HTTP连接"""
    print("\n🌐 测试HTTP连接...")
    
    urls = [
        "https://api.sgroup.qq.com",
        "https://sandbox.api.sgroup.qq.com", 
        "https://bots.qq.com",
        "https://bot.q.qq.com"
    ]
    
    timeout = aiohttp.ClientTimeout(total=10)
    
    async with aiohttp.ClientSession(timeout=timeout) as session:
        for url in urls:
            try:
                async with session.get(url) as response:
                    print(f"✅ {url} -> HTTP {response.status}")
            except aiohttp.ClientConnectorError as e:
                print(f"❌ {url} -> 连接失败: {e}")
            except asyncio.TimeoutError:
                print(f"⏰ {url} -> 连接超时")
            except Exception as e:
                print(f"❌ {url} -> 其他错误: {e}")

async def test_ssl_connection():
    """测试SSL连接"""
    print("\n🔒 测试SSL连接...")
    
    hosts = [
        ("api.sgroup.qq.com", 443),
        ("sandbox.api.sgroup.qq.com", 443),
        ("bots.qq.com", 443)
    ]
    
    for host, port in hosts:
        try:
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 测试SSL连接
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(host, port, ssl=context),
                timeout=10
            )
            writer.close()
            await writer.wait_closed()
            print(f"✅ {host}:{port} -> SSL连接成功")
        except asyncio.TimeoutError:
            print(f"⏰ {host}:{port} -> SSL连接超时")
        except Exception as e:
            print(f"❌ {host}:{port} -> SSL连接失败: {e}")

async def test_bot_api():
    """测试机器人API连接"""
    print("\n🤖 测试机器人API...")
    
    # 测试获取access token
    url = "https://bots.qq.com/app/getAppAccessToken"
    data = {
        "appId": config.BOT_ID,
        "clientSecret": config.BOT_SECRET
    }
    
    try:
        timeout = aiohttp.ClientTimeout(total=15)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url, json=data) as response:
                result = await response.json()
                if response.status == 200:
                    print(f"✅ 机器人API认证成功")
                    print(f"   Access Token: {result.get('access_token', 'N/A')[:20]}...")
                else:
                    print(f"❌ 机器人API认证失败: HTTP {response.status}")
                    print(f"   响应: {result}")
    except Exception as e:
        print(f"❌ 机器人API测试失败: {e}")

def check_proxy_settings():
    """检查代理设置"""
    print("\n🔧 检查代理设置...")
    
    import os
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    has_proxy = False
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"🔍 发现代理设置: {var}={value}")
            has_proxy = True
    
    if not has_proxy:
        print("✅ 未检测到代理设置")

async def main():
    """主函数"""
    print("🚀 QQ机器人网络连接诊断")
    print("=" * 50)
    
    # 检查代理设置
    check_proxy_settings()
    
    # 测试DNS解析
    await test_dns_resolution()
    
    # 测试HTTP连接
    await test_http_connection()
    
    # 测试SSL连接
    await test_ssl_connection()
    
    # 测试机器人API
    await test_bot_api()
    
    print("\n" + "=" * 50)
    print("🏁 网络诊断完成")
    
    print("\n💡 建议:")
    print("1. 如果DNS解析失败，请检查网络连接和DNS设置")
    print("2. 如果SSL连接失败，可能是防火墙或代理问题")
    print("3. 如果API认证失败，请检查BOT_ID和BOT_SECRET配置")
    print("4. 考虑切换到正式环境 (IS_SANDBOX = False)")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 诊断已取消")
    except Exception as e:
        print(f"\n❌ 诊断过程出错: {e}")
