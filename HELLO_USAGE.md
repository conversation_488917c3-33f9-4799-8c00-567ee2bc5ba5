# QQ机器人 Hello消息功能使用指南

## 📋 功能概述

根据QQ机器人官方文档要求，我们已经实现了在群聊中发送"hello"消息的功能。

## 🚀 快速开始

### 1. 启动Hello机器人

```bash
# 使用专门的Hello机器人
C:/Python311/python.exe hello_bot.py

# 或者使用基础机器人
C:/Python311/python.exe bot.py
```

### 2. 将机器人添加到群聊

1. 在QQ机器人管理后台配置沙箱群聊
2. 将机器人添加到测试群聊中
3. 确保机器人有发送消息的权限

### 3. 测试Hello功能

#### 方法一：自动发送
- 当机器人被添加到新群聊时，会自动发送hello欢迎消息

#### 方法二：手动触发
在群聊中@机器人并发送以下命令：
- `@机器人 发送hello`
- `@机器人 hello`

## 🤖 机器人响应示例

### 自动欢迎消息
```
hello! 我是QQ机器人，很高兴加入这个群聊！
```

### 手动触发响应
```
hello
```

## 📝 技术实现

### 核心功能代码

1. **群聊加入事件处理**
```python
async def on_group_add_robot(self, message):
    """当机器人被添加到群聊时触发"""
    await self.api.post_group_message(
        group_openid=message.group_openid,
        msg_type=0,  # 文本消息
        content="hello! 我是QQ机器人，很高兴加入这个群聊！"
    )
```

2. **手动发送Hello消息**
```python
async def on_group_at_message_create(self, message):
    """处理@消息"""
    if "发送hello" in message.content or "hello" in message.content.lower():
        await self.api.post_group_message(
            group_openid=message.group_openid,
            msg_type=0,
            content="hello"
        )
```

## 🔧 API调用说明

根据QQ机器人官方文档，发送群聊消息使用以下API：

- **接口地址**: `/v2/groups/{group_openid}/messages`
- **请求方法**: POST
- **消息类型**: 0 (文本消息)
- **内容**: "hello"

## ⚠️ 注意事项

1. **消息频率限制**：
   - 主动消息每月每个群限制4条
   - 被动消息有效期5分钟，每个消息最多回复5次

2. **权限要求**：
   - 机器人需要在群聊中有发送消息权限
   - 需要正确配置沙箱环境

3. **测试环境**：
   - 建议在沙箱环境中测试
   - 沙箱群成员不可超过20人

## 🎯 测试步骤

1. ✅ 启动Hello机器人
2. ✅ 机器人成功连接到QQ服务器
3. ✅ 将机器人添加到测试群聊
4. ✅ 机器人自动发送hello欢迎消息
5. ✅ 在群聊中@机器人发送"发送hello"命令
6. ✅ 机器人在群里发送"hello"消息

## 📞 支持

如果遇到问题，请检查：
- 机器人配置是否正确
- 网络连接是否正常
- 群聊权限是否足够
- 是否在沙箱环境中测试

## 📚 相关文档

- [QQ机器人官方文档](https://bot.q.qq.com/wiki/)
- [发送消息API文档](https://bot.q.qq.com/wiki/develop/api-v2/server-inter/message/send-receive/send.html)
- [群聊消息API](https://bot.q.qq.com/wiki/develop/api-v2/server-inter/message/send-receive/send.html#群聊)
