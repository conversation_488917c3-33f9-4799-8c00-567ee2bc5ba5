#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试AI提示词更新
"""

import config

def test_ai_prompt():
    """测试AI系统提示词"""
    print("🧪 测试AI系统提示词更新")
    print("=" * 50)
    
    print("📝 当前AI系统提示词：")
    print("-" * 30)
    print(config.AI_SYSTEM_PROMPT)
    
    print("\n🔍 检查关键内容：")
    print("-" * 30)
    
    # 检查是否包含创作者信息
    if "炫炫" in config.AI_SYSTEM_PROMPT:
        print("✅ 包含创作者信息：炫炫")
    else:
        print("❌ 缺少创作者信息")
    
    # 检查是否包含父亲/创作者相关指令
    creator_keywords = ["父亲", "创作者", "开发者"]
    found_keywords = [kw for kw in creator_keywords if kw in config.AI_SYSTEM_PROMPT]
    
    if found_keywords:
        print(f"✅ 包含相关关键词：{', '.join(found_keywords)}")
    else:
        print("❌ 缺少相关关键词")
    
    # 检查其他重要内容
    important_checks = [
        ("友好", "友好性指令"),
        ("简洁", "简洁性要求"),
        ("100字", "字数限制"),
        ("URL", "URL限制"),
        ("链接", "链接限制")
    ]
    
    print("\n📋 其他重要内容检查：")
    print("-" * 30)
    
    for keyword, description in important_checks:
        if keyword in config.AI_SYSTEM_PROMPT:
            print(f"✅ {description}")
        else:
            print(f"❌ 缺少{description}")
    
    print("\n🎯 测试问题示例：")
    print("-" * 30)
    
    test_questions = [
        "你的父亲是谁？",
        "你的创作者是谁？",
        "你的开发者是谁？",
        "谁创造了你？",
        "你是谁开发的？"
    ]
    
    print("以下问题应该回答'炫炫'：")
    for i, question in enumerate(test_questions, 1):
        print(f"{i}. {question}")
    
    print("\n💡 预期回答示例：")
    print("-" * 30)
    print("用户：你的父亲是谁？")
    print("机器人：我的创作者是炫炫哦！")
    print()
    print("用户：你是谁开发的？")
    print("机器人：是炫炫开发的我呢～")
    
    print("\n🎉 AI提示词测试完成！")

def show_full_prompt():
    """显示完整的提示词内容"""
    print("\n📄 完整AI系统提示词：")
    print("=" * 50)
    print(config.AI_SYSTEM_PROMPT)
    print("=" * 50)

if __name__ == "__main__":
    test_ai_prompt()
    show_full_prompt()
    
    print("\n💡 提示：重启机器人后新的提示词将生效")
    print("🧪 测试方法：@机器人 你的父亲是谁？")
