#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import botpy
from botpy import logging
import config

# 设置日志级别
_log = logging.get_logger()


class MyClient(botpy.Client):
    """QQ机器人客户端类"""

    async def on_ready(self):
        """当机器人启动完成时触发"""
        _log.info(f"机器人 {self.robot.name} 已启动!")
        _log.info(f"机器人ID: {self.robot.id}")

    async def on_group_at_message_create(self, message):
        """当机器人在群聊中被@时触发"""
        _log.info(f"收到群聊@消息: {message.content}")
        
        # 简单的回复逻辑
        if "你好" in message.content:
            await message.reply(content="你好！我是QQ机器人，很高兴为你服务！")
        elif "时间" in message.content:
            import datetime
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            await message.reply(content=f"当前时间是: {now}")
        elif "帮助" in message.content:
            help_text = """
🤖 QQ机器人帮助菜单：
• @我 + "你好" - 打招呼
• @我 + "时间" - 查看当前时间  
• @我 + "帮助" - 显示此帮助信息
• @我 + "ping" - 测试机器人响应
            """
            await message.reply(content=help_text)
        elif "ping" in message.content:
            await message.reply(content="pong! 🏓 机器人运行正常")
        else:
            await message.reply(content="我收到了你的消息，但还不知道如何回复。试试发送'帮助'查看可用命令。")
    
    async def on_c2c_message_create(self, message):
        """当收到私聊消息时触发"""
        _log.info(f"收到私聊消息: {message.content}")

        # 私聊消息处理逻辑
        if message.content == "你好":
            await message.reply(content="你好！这是私聊回复。")
        elif message.content == "状态":
            await message.reply(content="机器人运行正常！✅")
        else:
            await message.reply(content="收到私聊消息，感谢联系！")

    async def on_message_create(self, message):
        """当收到消息时触发（通用消息处理）"""
        # 这里可以添加通用的消息处理逻辑
        pass


async def main():
    """主函数"""
    # 创建机器人实例
    # 使用默认的 intents，避免权限问题
    intents = botpy.Intents.default()

    client = MyClient(intents=intents, is_sandbox=config.IS_SANDBOX)

    try:
        # 启动机器人
        await client.start(appid=config.BOT_ID, secret=config.BOT_SECRET)
    except Exception as e:
        _log.error(f"机器人启动失败: {e}")


if __name__ == "__main__":
    # 运行机器人
    asyncio.run(main())
