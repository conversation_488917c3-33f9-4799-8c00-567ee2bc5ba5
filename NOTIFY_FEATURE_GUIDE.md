# 🔔 QQ机器人@全体提醒功能指南

## 🎉 功能概述

为了让更多群员参与抽奖活动，我们添加了@全体成员提醒功能！

### ✨ 新增功能

1. **抽奖开始提醒** - 创建抽奖时自动@everyone通知群员
2. **开奖结果提醒** - 开奖时@everyone公布结果
3. **灵活控制** - 管理员可以开启/关闭@everyone功能
4. **智能降级** - 如果@everyone失败，自动发送普通提醒

## 🚀 使用方法

### 创建抽奖（自动提醒）

```
@机器人 创建抽奖 7 2小时后
```

**机器人会自动发送：**
```
🎉 抽奖活动开始啦！@everyone

📋 活动详情：
• 选取范围: 最近7天的消息
• 开奖时间: 2小时后
• 参与方式: @机器人发送任意消息即可参与

💡 参与示例：
@机器人 今天天气不错
@机器人 大家好
@机器人 我要参与抽奖

🎯 快来参与吧，好运等着你！
```

### 开奖结果（自动提醒）

```
@机器人 手动开奖
```

**机器人会自动发送：**
```
🎉 抽奖结果公布！@everyone

🏆 获奖用户: 用户ID
💬 获奖消息: 获奖的消息内容
📅 发言时间: 消息发送时间
📊 参与消息数: 15条
🎫 抽奖ID: group123_1704117600

🎊 恭喜获奖者！感谢大家的参与！
```

## ⚙️ 管理员设置

### 查看当前设置

```
@机器人 提醒设置
```

**回复：**
```
📢 抽奖提醒设置：

🔔 全体提醒状态: 开启
📝 说明: 控制抽奖开始和结束时是否@全体成员

⚙️ 管理指令:
• 开启全体提醒 - 开启@everyone提醒
• 关闭全体提醒 - 关闭@everyone提醒
```

### 开启@everyone提醒

```
@机器人 开启全体提醒
```

**回复：** `✅ 已开启抽奖全体提醒功能`

### 关闭@everyone提醒

```
@机器人 关闭全体提醒
```

**回复：** `✅ 已关闭抽奖全体提醒功能`

## 🔧 配置文件设置

在 `config.py` 中可以设置默认行为：

```python
# 抽奖提醒配置
LOTTERY_NOTIFY_ALL = True  # 是否在抽奖开始和结束时@全体成员
```

- `True` - 默认开启@everyone提醒
- `False` - 默认关闭@everyone提醒

## 📋 完整指令列表

### 👑 管理员指令

| 指令 | 功能 | 示例 |
|------|------|------|
| 创建抽奖 | 创建抽奖活动（自动提醒） | `@机器人 创建抽奖 7 2小时后` |
| 手动开奖 | 立即开奖（自动提醒） | `@机器人 手动开奖` |
| 开启全体提醒 | 开启@everyone功能 | `@机器人 开启全体提醒` |
| 关闭全体提醒 | 关闭@everyone功能 | `@机器人 关闭全体提醒` |
| 提醒设置 | 查看当前提醒设置 | `@机器人 提醒设置` |

### 👥 用户指令

| 指令 | 功能 | 示例 |
|------|------|------|
| 参与抽奖 | @机器人发送任意非指令消息 | `@机器人 今天天气不错` |
| 抽奖状态 | 查看当前抽奖状态 | `@机器人 抽奖状态` |
| 抽奖历史 | 查看历史抽奖记录 | `@机器人 抽奖历史` |

## 🎯 使用场景

### 场景1：活跃群聊
- **建议：开启@everyone提醒**
- **效果：最大化参与度，让所有群员都知道抽奖活动**

### 场景2：安静群聊
- **建议：关闭@everyone提醒**
- **效果：避免过度打扰，只有主动关注的用户参与**

### 场景3：大型群聊
- **建议：谨慎使用@everyone**
- **效果：避免引起反感，可以在特殊活动时开启**

## 🔍 技术细节

### 智能降级机制

1. **首次尝试** - 发送带@everyone的完整提醒消息
2. **失败降级** - 如果@everyone被限制，自动发送简化版普通消息
3. **错误处理** - 记录错误日志，确保功能稳定

### 权限控制

- **管理员权限** - 只有管理员和群主可以修改提醒设置
- **群主优先** - 群主自动拥有所有管理权限
- **配置管理员** - 通过config.py配置的管理员也有完整权限

### 消息格式

- **统一风格** - 所有提醒消息使用统一的emoji和格式
- **信息完整** - 包含所有必要的抽奖信息
- **用户友好** - 清晰的参与指导和示例

## 🎊 使用建议

### 最佳实践

1. **合理使用** - 不要频繁开启关闭@everyone功能
2. **群规遵守** - 确保@everyone符合群聊规则
3. **时机选择** - 在合适的时间创建抽奖活动
4. **参与引导** - 鼓励群员积极参与抽奖

### 注意事项

1. **权限限制** - 某些群聊可能限制@everyone功能
2. **消息频率** - 避免短时间内多次@everyone
3. **群员感受** - 考虑群员对@everyone的接受度
4. **备用方案** - 如果@everyone不可用，使用普通提醒

## 🚀 快速开始

1. **重启机器人**：
   ```bash
   C:/Python311/python.exe ai_lottery_bot.py
   ```

2. **创建抽奖**：
   ```
   @机器人 创建抽奖 7 1小时后
   ```

3. **观察效果** - 机器人会自动@everyone通知群员

4. **管理设置**：
   ```
   @机器人 提醒设置
   ```

现在你的QQ机器人具备了完整的@全体提醒功能！🎉🔔✨
