# 🤖 QQ机器人AI功能配置指南

## 📋 功能概述

我们已经成功为QQ机器人集成了OpenAI大模型功能！现在机器人可以：

- 🧠 智能回复用户的任意消息
- 💬 支持上下文对话记忆
- 🎯 区分预定义指令和AI对话
- 📝 维护每个用户的对话历史

## 🚀 快速配置

### 1. 配置OpenAI API

在 `config.py` 文件中设置以下参数：

```python
# OpenAI大模型配置
OPENAI_API_KEY = "sk-your-actual-api-key-here"  # 替换为真实的API Key
OPENAI_API_BASE = "https://api.openai.com/v1"   # API地址
OPENAI_MODEL = "gpt-3.5-turbo"                  # 使用的模型

# AI回复配置
AI_ENABLED = True                               # 启用AI功能
AI_MAX_TOKENS = 500                            # 最大回复长度
AI_TEMPERATURE = 0.7                           # 创造性程度
AI_SYSTEM_PROMPT = "你是一个友好的QQ群聊机器人助手..."  # 系统提示词
```

### 2. 获取OpenAI API Key

1. 访问 [OpenAI官网](https://platform.openai.com/)
2. 注册/登录账号
3. 进入API Keys页面
4. 创建新的API Key
5. 复制API Key到配置文件

### 3. 替代API服务

如果无法使用OpenAI官方API，可以使用兼容的第三方服务：

```python
# 示例：使用其他兼容OpenAI API的服务
OPENAI_API_BASE = "https://your-alternative-api.com/v1"
OPENAI_API_KEY = "your-alternative-api-key"
```

## 🎯 使用方法

### 启动AI机器人

```bash
# 启动AI智能机器人
C:/Python311/python.exe ai_bot.py
```

### 功能测试

1. **预定义指令**（不会发送给AI）：
   - `@机器人 帮助` - 显示帮助信息
   - `@机器人 时间` - 显示当前时间
   - `@机器人 ping` - 测试响应
   - `@机器人 状态` - 显示机器人状态
   - `@机器人 ai状态` - 显示AI功能状态
   - `@机器人 清除历史` - 清除对话历史

2. **AI智能对话**（其他所有消息）：
   - `@机器人 你好，请介绍一下自己`
   - `@机器人 今天天气怎么样？`
   - `@机器人 帮我写一首诗`
   - `@机器人 解释一下量子计算`

## 🔧 技术特性

### 对话记忆功能
- 每个用户独立的对话历史
- 自动维护上下文关系
- 5分钟无活动自动清理缓存
- 最多保留20条历史消息

### 智能指令识别
```python
# 预定义指令列表
predefined_commands = [
    "帮助", "help", "时间", "time", "ping", "状态", "status",
    "清除历史", "clear", "ai状态", "ai_status"
]
```

### 错误处理
- API请求超时处理
- 网络错误重试机制
- 优雅的错误提示

## 📁 项目文件说明

### 新增文件：
- **`ai_assistant.py`** - AI助手核心模块
- **`ai_bot.py`** - AI智能机器人主程序
- **`AI_SETUP_GUIDE.md`** - 本配置指南

### 更新文件：
- **`config.py`** - 添加了OpenAI配置
- **`requirements.txt`** - 添加了openai和tiktoken依赖

## 🧪 测试步骤

### 1. 测试AI助手模块
```bash
C:/Python311/python.exe ai_assistant.py
```

### 2. 启动AI机器人
```bash
C:/Python311/python.exe ai_bot.py
```

### 3. 群聊测试
1. 将机器人添加到测试群聊
2. @机器人发送 `帮助` 查看功能
3. @机器人发送 `ai状态` 检查AI功能
4. @机器人发送任意消息测试AI回复

## ⚠️ 注意事项

### API费用
- OpenAI API按使用量计费
- 建议设置合理的 `AI_MAX_TOKENS` 限制
- 监控API使用量避免超额

### 安全性
- 妥善保管API Key，不要泄露
- 可以设置API使用限制
- 定期轮换API Key

### 性能优化
- 对话历史自动清理
- API请求超时设置
- 错误重试机制

## 🔍 故障排除

### 常见问题：

1. **AI功能未启用**
   - 检查 `OPENAI_API_KEY` 是否正确设置
   - 确认 `AI_ENABLED = True`

2. **API请求失败**
   - 检查网络连接
   - 验证API Key有效性
   - 确认API余额充足

3. **回复超时**
   - 检查 `OPENAI_API_BASE` 地址
   - 尝试更换API服务商
   - 调整超时设置

## 🎊 成功标志

当看到以下输出时，表示AI功能配置成功：

```
✅ AI智能机器人 早鸟-测试中 已启动!
📱 机器人ID: 1411261171501187073
🤖 AI功能已启用
   模型: gpt-3.5-turbo
   API地址: https://api.openai.com/v1
🎯 机器人正在等待消息...
```

现在你的QQ机器人已经具备了强大的AI对话能力！🚀
