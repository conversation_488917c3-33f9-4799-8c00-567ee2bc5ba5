#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试用户ID获取功能
"""

class MockAuthor:
    """模拟消息作者对象"""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def __str__(self):
        return f"MockAuthor({self.__dict__})"

class MockMessage:
    """模拟消息对象"""
    def __init__(self, author):
        self.author = author

def get_user_id(message):
    """获取用户ID，兼容不同的消息类型"""
    author = message.author
    # 尝试多种可能的用户ID属性
    user_id = (
        getattr(author, 'id', None) or 
        getattr(author, 'openid', None) or 
        getattr(author, 'user_openid', None) or
        getattr(author, 'member_openid', None) or
        str(hash(str(author)))  # 最后的备选方案
    )
    return str(user_id)

def test_user_id_extraction():
    """测试用户ID提取功能"""
    print("🧪 测试用户ID提取功能")
    print("=" * 40)
    
    # 测试用例1：有id属性
    author1 = MockAuthor(id="12345")
    message1 = MockMessage(author1)
    user_id1 = get_user_id(message1)
    print(f"✅ 测试1 - 有id属性: {user_id1}")
    
    # 测试用例2：有openid属性
    author2 = MockAuthor(openid="openid_67890")
    message2 = MockMessage(author2)
    user_id2 = get_user_id(message2)
    print(f"✅ 测试2 - 有openid属性: {user_id2}")
    
    # 测试用例3：有user_openid属性
    author3 = MockAuthor(user_openid="user_openid_abcde")
    message3 = MockMessage(author3)
    user_id3 = get_user_id(message3)
    print(f"✅ 测试3 - 有user_openid属性: {user_id3}")
    
    # 测试用例4：没有任何ID属性，使用hash
    author4 = MockAuthor(name="test_user")
    message4 = MockMessage(author4)
    user_id4 = get_user_id(message4)
    print(f"✅ 测试4 - 使用hash: {user_id4}")
    
    # 测试用例5：空对象
    author5 = MockAuthor()
    message5 = MockMessage(author5)
    user_id5 = get_user_id(message5)
    print(f"✅ 测试5 - 空对象: {user_id5}")
    
    print("\n🎉 所有测试完成！用户ID提取功能正常工作。")

if __name__ == "__main__":
    test_user_id_extraction()
