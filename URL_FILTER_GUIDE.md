# 🔒 QQ机器人URL过滤和白名单配置指南

## 📋 问题说明

### 遇到的错误
```
消息发送失败, 不允许发送url t.circle,t.circle
```

这个错误表明QQ机器人平台对消息中的URL有严格的白名单限制。

## ✅ 已实施的解决方案

### 1. 内容过滤系统
我们已经在AI助手中实现了内容过滤功能：

```python
def _filter_content(self, content: str) -> str:
    """过滤AI回复内容，移除不被允许的URL和敏感内容"""
    # 移除所有HTTP/HTTPS链接
    url_patterns = [
        r'https?://[^\s]*t\.circle[^\s]*',  # t.circle相关URL
        r'https?://[^\s]*\.circle[^\s]*',   # 其他.circle域名
        r'https?://[^\s]*circle[^\s]*',     # 包含circle的URL
        r'https?://[^\s]+',                 # 所有HTTP/HTTPS链接
    ]
    
    # 替换为安全文本
    for pattern in url_patterns:
        content = re.sub(pattern, '[链接已过滤]', content, flags=re.IGNORECASE)
```

### 2. 绘画请求特殊处理
对于绘画相关请求，我们提供文字描述而不是图片链接：

```python
def _handle_drawing_request(self, user_message: str) -> str:
    """处理绘画请求，返回文字描述而不是图片链接"""
    if any(keyword in user_message.lower() for keyword in ['画', '绘制', '绘画']):
        return "我理解你想要画XXX！虽然我无法直接生成图片，但我可以为你描述如何画..."
```

### 3. 系统提示词优化
更新了AI系统提示词，明确禁止包含URL：

```python
AI_SYSTEM_PROMPT = "你是一个友好的QQ群聊机器人助手，请用简洁、有趣的方式回复用户的消息。回复长度控制在100字以内。重要：不要在回复中包含任何URL链接、网址或链接相关的内容。"
```

## 🔧 QQ机器人平台URL白名单配置

### 在开发者后台配置
1. 登录 [QQ机器人开放平台](https://bot.q.qq.com/open)
2. 进入你的机器人管理页面
3. 找到"开发设置" -> "消息URL白名单配置"
4. 添加允许的域名

### 白名单配置规则
- 只能添加域名，不能添加完整URL
- 域名需要ICP备案
- 每个机器人最多20个域名
- 每年可修改50次

### 示例配置
```
# 允许的域名示例
example.com
api.example.com
cdn.example.com
```

## 🛡️ 内容安全策略

### 当前过滤规则
1. **URL过滤**：移除所有HTTP/HTTPS链接
2. **敏感词过滤**：移除可能触发限制的词汇
3. **特殊请求处理**：绘画请求返回文字描述

### 过滤效果示例
```
原文: "你可以访问 https://t.circle/abc123 查看更多信息"
过滤后: "你可以访问 [链接已过滤] 查看更多信息"

原文: "画只小鸟"
处理后: "我理解你想要画小鸟！虽然我无法直接生成图片，但我可以为你描述如何画小鸟..."
```

## 🎯 测试验证

### 测试命令
```bash
# 测试内容过滤功能
python test_content_filter.py

# 重启AI机器人
python ai_bot.py
```

### 测试用例
在群聊中测试以下消息：
- `@机器人 画只小鸟` - 应该返回绘画指导而不是图片链接
- `@机器人 你好` - 正常AI对话
- `@机器人 介绍一下自己` - 确保回复中没有URL

## 📝 开发建议

### 1. 避免URL生成
- AI回复中不包含任何链接
- 使用文字描述替代图片链接
- 提供操作指导而不是外部资源

### 2. 内容审核
- 所有AI回复都经过内容过滤
- 敏感词汇自动替换
- 保持回复的安全性

### 3. 用户体验
- 绘画请求提供详细的文字指导
- 解释为什么无法提供图片
- 引导用户使用其他方式获取资源

## 🔍 故障排除

### 如果仍然出现URL错误
1. 检查AI回复是否包含隐藏的URL格式
2. 更新过滤规则覆盖新的URL模式
3. 在开发者后台添加必要的域名白名单
4. 联系QQ机器人技术支持

### 监控和日志
- 观察过滤前后的内容变化
- 记录被过滤的URL模式
- 持续优化过滤规则

现在AI机器人应该能够安全地回复用户消息，不会再触发URL限制错误！🛡️✨
